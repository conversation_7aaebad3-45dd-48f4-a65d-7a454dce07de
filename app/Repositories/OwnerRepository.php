<?php

declare(strict_types=1);

namespace App\Repositories;

use App\Models\Owner;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

/**
 * Owner Repository
 * 
 * Handles data access for owner-related operations
 */
class OwnerRepository
{
    /**
     * Find owner by ID with relationships
     */
    public function findWithRelations(int $ownerId): ?Owner
    {
        return Owner::with([
            'contracts',
            'plots',
            'payments',
            'personalUse',
            'parent',
            'children'
        ])->find($ownerId);
    }

    /**
     * Get owners by IDs
     */
    public function findByIds(array $ownerIds): Collection
    {
        return Owner::whereIn('id', $ownerIds)->get();
    }

    /**
     * Get owners with their inheritance relationships
     */
    public function getOwnersWithInheritance(array $ownerIds = []): Collection
    {
        $query = Owner::with(['parent', 'children']);

        if (!empty($ownerIds)) {
            $query->whereIn('id', $ownerIds);
        }

        return $query->get();
    }

    /**
     * Get owners who died in a specific farming year
     */
    public function getOwnersDeadInYear(int $year): Collection
    {
        return Owner::deadInFarmingYear($year)->get();
    }

    /**
     * Get living owners
     */
    public function getLivingOwners(): Collection
    {
        return Owner::alive()->get();
    }

    /**
     * Get owners by contract
     */
    public function getOwnersByContract(int $contractId): Collection
    {
        return Owner::whereHas('contracts', function ($query) use ($contractId) {
            $query->where('contract_id', $contractId);
        })->get();
    }

    /**
     * Get owner hierarchy (parent-child relationships)
     */
    public function getOwnerHierarchy(int $rootOwnerId): array
    {
        $owner = $this->findWithRelations($rootOwnerId);
        
        if (!$owner) {
            return [];
        }

        return $this->buildOwnerTree($owner);
    }

    /**
     * Build owner tree recursively
     */
    private function buildOwnerTree(Owner $owner): array
    {
        $ownerData = $owner->toArray();
        $ownerData['children'] = [];

        foreach ($owner->children as $child) {
            $ownerData['children'][] = $this->buildOwnerTree($child);
        }

        return $ownerData;
    }

    /**
     * Get owners with their plot ownership data
     */
    public function getOwnersWithPlotData(array $filterParams = []): array
    {
        $query = DB::table('owners as o')
            ->join('plot_contracts as pc', 'o.id', '=', 'pc.owner_id')
            ->join('plots as p', 'pc.plot_id', '=', 'p.id')
            ->join('contracts as c', 'pc.contract_id', '=', 'c.id')
            ->select([
                'o.*',
                'pc.path',
                'pc.plots_percent',
                'pc.plot_owned_area',
                'pc.plot_owned_area_total',
                'p.kad_ident',
                'p.cultivated_area',
                'c.id as contract_id',
                'c.c_num',
                'c.farming_name',
            ]);

        // Apply filters
        if (!empty($filterParams['owner_ids'])) {
            $query->whereIn('o.id', $filterParams['owner_ids']);
        }

        if (!empty($filterParams['contract_ids'])) {
            $query->whereIn('c.id', $filterParams['contract_ids']);
        }

        if (!empty($filterParams['farming_ids'])) {
            $query->whereIn('c.farming_id', $filterParams['farming_ids']);
        }

        return $query->get()->toArray();
    }

    /**
     * Check if owner has allowed farming access
     */
    public function hasAllowedFarmingAccess(int $ownerId, array $allowedFarmingIds): bool
    {
        return DB::table('owners as o')
            ->join('plot_contracts as pc', 'o.id', '=', 'pc.owner_id')
            ->join('contracts as c', 'pc.contract_id', '=', 'c.id')
            ->where('o.id', $ownerId)
            ->whereIn('c.farming_id', $allowedFarmingIds)
            ->exists();
    }

    /**
     * Get owner's total area by contract
     */
    public function getOwnerAreaByContract(int $ownerId, int $contractId): string
    {
        $result = DB::table('plot_contracts')
            ->where('owner_id', $ownerId)
            ->where('contract_id', $contractId)
            ->sum('plot_owned_area');

        return (string) ($result ?? '0');
    }

    /**
     * Get owners with pagination
     */
    public function getPaginatedOwners(int $page, int $rows, array $ownerIds = []): array
    {
        $query = Owner::query();

        if (!empty($ownerIds)) {
            $query->whereIn('id', $ownerIds);
        }

        $start = ($page - 1) * $rows;
        
        return $query->skip($start)->take($rows)->get()->toArray();
    }
}
