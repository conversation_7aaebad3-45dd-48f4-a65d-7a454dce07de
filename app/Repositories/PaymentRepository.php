<?php

declare(strict_types=1);

namespace App\Repositories;

use App\Models\Payment;
use App\Models\Plot;
use App\Models\Owner;
use App\Models\Contract;
use App\Models\PersonalUse;
use App\Models\RentType;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

/**
 * Payment Repository
 * 
 * Handles data access for payment-related operations
 * Migrated from legacy PaymentsModel functionality
 */
class PaymentRepository
{
    /**
     * Get payment plots with owners, rents, and contract data
     * 
     * Migrated from getPaymentPlots() method
     */
    public function getPaymentPlots(
        int $year,
        ?int $contractAnnexId = null,
        ?int $ownerId = null,
        ?string $path = null,
        array $filterParams = []
    ): array {
        $farmYearStart = ($year - 1) . '-10-01';
        $farmYearEnd = $year . '-09-30';

        $query = DB::table('plots as kvs')
            ->join('plot_contracts as pc', 'kvs.id', '=', 'pc.plot_id')
            ->join('contracts as c', 'pc.contract_id', '=', 'c.id')
            ->join('owners as o', 'pc.owner_id', '=', 'o.id')
            ->leftJoin('contracts as a', 'c.parent_id', '=', 'a.id')
            ->leftJoin('contract_groups as cg', 'c.contract_group_id', '=', 'cg.id')
            ->leftJoin('plot_rent_types as pr', 'pc.id', '=', 'pr.pc_rel_id')
            ->leftJoin('rent_types as rt', 'pr.rent_type_id', '=', 'rt.id')
            ->select([
                'c.id as contract_id',
                'c.parent_id',
                'c.c_num',
                'cg.name as contract_group_name',
                'c.farming_id',
                'c.farming_name',
                'c.virtual_contract_type as contract_type',
                'c.start_date as contract_start_date',
                'c.due_date as contract_due_date',
                'c.osz_num',
                'c.osz_date',
                'c.sv_num',
                'c.sv_date',
                'a.id as annex_id',
                'pc.id as pc_rel_id',
                'pr.id as plot_rent_type_id',
                'kvs.id as plot_id',
                'kvs.kad_ident',
                'kvs.mestnost',
                'kvs.virtual_category_title as category',
                'kvs.virtual_ekatte_name as ekatte_name',
                'kvs.virtual_ntp_title as area_type',
                'kvs.cultivated_area',
                'o.id as owner_id',
                'o.owner_names',
                'o.egn_eik',
                'o.phone',
                'o.mobile',
                'o.address',
                'o.company_address',
                'o.lk_nomer',
                'o.lk_izdavane',
                'o.iban',
                'o.is_dead',
                'o.dead_date',
                'o.allow_owner_payment',
                'o.post_payment_fields as owner_post_payment_fields',
                'pc.path',
                'pc.plots_percent',
                'pc.plot_owned_area',
                'pc.plot_owned_area_total',
                'pc.plot_owned_contract_area',
                'c.rent_money_value',
                'pr.rent_per_plot_value',
                'pr.rent_per_plot',
            ]);

        // Apply filters
        if ($ownerId) {
            $query->where('o.id', $ownerId);
            if (!$path) {
                $query->whereNull('pc.path');
            }
        }

        if ($path) {
            $query->where('pc.path', $path);
        }

        if ($contractAnnexId) {
            $query->where('c.id', $contractAnnexId);
        }

        if (!empty($filterParams['payroll_ekate'])) {
            $query->whereIn('kvs.ekate', $filterParams['payroll_ekate']);
        }

        if (!empty($filterParams['payroll_farming'])) {
            $hasNullValue = in_array(null, $filterParams['payroll_farming']);
            $farmingIds = array_filter($filterParams['payroll_farming'], fn($value) => $value !== null);
            
            $query->where(function ($q) use ($farmingIds, $hasNullValue) {
                if (!empty($farmingIds)) {
                    $q->whereIn('c.farming_id', $farmingIds);
                }
                if ($hasNullValue) {
                    $q->orWhereNull('c.farming_id');
                }
            });
        }

        return $query->get()->toArray();
    }

    /**
     * Get natural rent types
     */
    public function getNatRents(): array
    {
        return RentType::natureTypes()
            ->active()
            ->orderBy('sort_order')
            ->get()
            ->toArray();
    }

    /**
     * Get owner contracts for a specific year
     */
    public function getOwnerContracts(int $year, array $filterParams = []): array
    {
        $query = DB::table('contracts as c')
            ->join('plot_contracts as pc', 'c.id', '=', 'pc.contract_id')
            ->join('owners as o', 'pc.owner_id', '=', 'o.id')
            ->select([
                'c.id as contract_id',
                'c.parent_id',
                'c.c_num',
                'c.farming_id',
                'c.farming_name',
                DB::raw('GROUP_CONCAT(DISTINCT pc.owner_id) as owner_ids'),
                DB::raw('GROUP_CONCAT(DISTINCT o.parent_id) as parent_ids'),
            ])
            ->groupBy([
                'c.id',
                'c.parent_id',
                'c.c_num',
                'c.farming_id',
                'c.farming_name'
            ]);

        // Apply filters
        if (!empty($filterParams['owner_id'])) {
            $query->where('pc.owner_id', $filterParams['owner_id']);
        }

        if (!empty($filterParams['payroll_farming'])) {
            $hasNullValue = in_array(null, $filterParams['payroll_farming']);
            $farmingIds = array_filter($filterParams['payroll_farming'], fn($value) => $value !== null);
            
            $query->where(function ($q) use ($farmingIds, $hasNullValue) {
                if (!empty($farmingIds)) {
                    $q->whereIn('c.farming_id', $farmingIds);
                }
                if ($hasNullValue) {
                    $q->orWhereNull('c.farming_id');
                }
            });
        }

        return $query->get()->toArray();
    }

    /**
     * Get payments for owners and contracts
     */
    public function getOwnerPayments(
        int $year,
        array $contractAnnexIds = [],
        ?int $ownerId = null,
        ?string $path = null
    ): array {
        $query = Payment::query()
            ->with(['owner', 'contract', 'rentType'])
            ->forYear($year);

        if (!empty($contractAnnexIds)) {
            $query->whereIn('contract_id', $contractAnnexIds);
        }

        if ($ownerId) {
            $query->where('owner_id', $ownerId);
        }

        return $query->get()->toArray();
    }

    /**
     * Get personal use data for owners
     */
    public function getPersonalUseForOwners(array $criteria): array
    {
        $query = PersonalUse::query()
            ->with(['owner', 'contract', 'rentType']);

        if (isset($criteria['contract_id'])) {
            $query->where('contract_id', $criteria['contract_id']);
        }

        if (isset($criteria['year'])) {
            $query->where('year', $criteria['year']);
        }

        return $query->get()->toArray();
    }
}
