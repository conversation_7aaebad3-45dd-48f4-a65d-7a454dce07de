<?php

declare(strict_types=1);

namespace App\Repositories;

use App\Models\Contract;
use App\Models\Owner;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

/**
 * Contract Repository
 * 
 * Handles data access for contract-related operations
 */
class ContractRepository
{
    /**
     * Find contract by ID with relationships
     */
    public function findWithRelations(int $contractId): ?Contract
    {
        return Contract::with([
            'parent',
            'annexes',
            'owners',
            'plots',
            'payments',
            'rentTypes'
        ])->find($contractId);
    }

    /**
     * Get contracts by IDs
     */
    public function findByIds(array $contractIds): Collection
    {
        return Contract::whereIn('id', $contractIds)->get();
    }

    /**
     * Get contracts for a specific farming year
     */
    public function getContractsForYear(int $year, array $filterParams = []): Collection
    {
        $query = Contract::forFarmingYear($year);

        if (!empty($filterParams['farming_ids'])) {
            $hasNullValue = in_array(null, $filterParams['farming_ids']);
            $farmingIds = array_filter($filterParams['farming_ids'], fn($value) => $value !== null);
            
            $query->where(function ($q) use ($farmingIds, $hasNullValue) {
                if (!empty($farmingIds)) {
                    $q->whereIn('farming_id', $farmingIds);
                }
                if ($hasNullValue) {
                    $q->orWhereNull('farming_id');
                }
            });
        }

        return $query->get();
    }

    /**
     * Get main contracts (not annexes)
     */
    public function getMainContracts(): Collection
    {
        return Contract::mainContracts()->get();
    }

    /**
     * Get annexes for a contract
     */
    public function getAnnexes(int $contractId): Collection
    {
        return Contract::where('parent_id', $contractId)->get();
    }

    /**
     * Get contract with its effective ID (parent if annex)
     */
    public function getEffectiveContract(int $contractId): ?Contract
    {
        $contract = Contract::find($contractId);
        
        if (!$contract) {
            return null;
        }

        if ($contract->isAnnex()) {
            return Contract::find($contract->parent_id);
        }

        return $contract;
    }

    /**
     * Get contracts by owner
     */
    public function getContractsByOwner(int $ownerId, int $year = null): Collection
    {
        $query = Contract::whereHas('owners', function ($q) use ($ownerId) {
            $q->where('owner_id', $ownerId);
        });

        if ($year) {
            $query->forFarmingYear($year);
        }

        return $query->get();
    }

    /**
     * Get contract owners with their relationships
     */
    public function getContractOwners(int $contractId): Collection
    {
        return Owner::whereHas('contracts', function ($query) use ($contractId) {
            $query->where('contract_id', $contractId);
        })->with(['parent', 'children'])->get();
    }

    /**
     * Get contract rent types with values
     */
    public function getContractRentTypes(int $contractId): array
    {
        return DB::table('contract_rent_types as crt')
            ->join('rent_types as rt', 'crt.rent_type_id', '=', 'rt.id')
            ->where('crt.contract_id', $contractId)
            ->select([
                'rt.*',
                'crt.nat_value',
                'crt.unit_value',
                'crt.unit_id'
            ])
            ->get()
            ->toArray();
    }

    /**
     * Get contract plots with owner data
     */
    public function getContractPlotsWithOwners(int $contractId): array
    {
        return DB::table('plot_contracts as pc')
            ->join('plots as p', 'pc.plot_id', '=', 'p.id')
            ->join('owners as o', 'pc.owner_id', '=', 'o.id')
            ->join('contracts as c', 'pc.contract_id', '=', 'c.id')
            ->where('pc.contract_id', $contractId)
            ->select([
                'p.*',
                'o.id as owner_id',
                'o.owner_names',
                'o.is_dead',
                'o.dead_date',
                'pc.path',
                'pc.plots_percent',
                'pc.plot_owned_area',
                'pc.plot_owned_area_total',
                'c.rent_money_value',
            ])
            ->get()
            ->toArray();
    }

    /**
     * Check if contract has specific owner
     */
    public function hasOwner(int $contractId, int $ownerId): bool
    {
        return DB::table('plot_contracts')
            ->where('contract_id', $contractId)
            ->where('owner_id', $ownerId)
            ->exists();
    }

    /**
     * Get contract total area
     */
    public function getContractTotalArea(int $contractId): string
    {
        $result = DB::table('plot_contracts as pc')
            ->join('plots as p', 'pc.plot_id', '=', 'p.id')
            ->where('pc.contract_id', $contractId)
            ->sum('p.cultivated_area');

        return (string) ($result ?? '0');
    }

    /**
     * Get owner's area in contract
     */
    public function getOwnerAreaInContract(int $contractId, int $ownerId): string
    {
        $result = DB::table('plot_contracts')
            ->where('contract_id', $contractId)
            ->where('owner_id', $ownerId)
            ->sum('plot_owned_area');

        return (string) ($result ?? '0');
    }
}
