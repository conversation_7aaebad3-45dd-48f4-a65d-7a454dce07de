<?php

declare(strict_types=1);

namespace App\DTOs;

/**
 * Data Transfer Object for Payroll Response
 * 
 * Handles output data structure for owner payroll calculations
 */
class PayrollResponseDTO
{
    public function __construct(
        public readonly array $rows,
        public readonly int $total,
        public readonly array $footer = []
    ) {}

    /**
     * Create DTO from array
     */
    public static function fromArray(array $data): self
    {
        return new self(
            rows: $data['rows'] ?? [],
            total: $data['total'] ?? 0,
            footer: $data['footer'] ?? []
        );
    }

    /**
     * Convert to array
     */
    public function toArray(): array
    {
        return [
            'rows' => $this->rows,
            'total' => $this->total,
            'footer' => $this->footer,
        ];
    }
}
