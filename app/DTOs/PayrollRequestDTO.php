<?php

declare(strict_types=1);

namespace App\DTOs;

/**
 * Data Transfer Object for Payroll Request
 * 
 * Handles input parameters for owner payroll calculations
 */
class PayrollRequestDTO
{
    public function __construct(
        public readonly int $year,
        public readonly ?int $ownerId = null,
        public readonly ?string $path = null,
        public readonly array $filterParams = [],
        public readonly ?int $rows = null,
        public readonly ?int $page = null,
        public readonly bool $returnFlatOwners = false
    ) {}

    /**
     * Create DTO from array
     */
    public static function fromArray(array $data): self
    {
        return new self(
            year: $data['year'],
            ownerId: $data['owner_id'] ?? null,
            path: $data['path'] ?? null,
            filterParams: $data['filter_params'] ?? [],
            rows: $data['rows'] ?? null,
            page: $data['page'] ?? null,
            returnFlatOwners: $data['return_flat_owners'] ?? false
        );
    }

    /**
     * Convert to array
     */
    public function toArray(): array
    {
        return [
            'year' => $this->year,
            'owner_id' => $this->ownerId,
            'path' => $this->path,
            'filter_params' => $this->filterParams,
            'rows' => $this->rows,
            'page' => $this->page,
            'return_flat_owners' => $this->returnFlatOwners,
        ];
    }
}
