<?php

declare(strict_types=1);

namespace App\DTOs;

/**
 * Data Transfer Object for Owner Payroll Data
 * 
 * Represents complex owner payroll information including
 * rent calculations, personal use, payments, and inheritance data
 */
class OwnerPayrollDTO
{
    public function __construct(
        public readonly int $ownerId,
        public readonly string $ownerNames,
        public readonly ?string $path = null,
        public readonly ?string $ownerPathKey = null,
        public readonly string $contractId = '',
        public readonly ?string $parentId = null,
        public readonly string $contractNumber = '',
        public readonly string $farmingName = '',
        public readonly string $allOwnerArea = '0',
        public readonly string $ownerArea = '0',
        public readonly string $personalUseArea = '0',
        public readonly string $renta = '0',
        public readonly string $chargedRenta = '0',
        public readonly string $paidRenta = '0',
        public readonly string $unpaidRenta = '0',
        public readonly string $overpaidRenta = '0',
        public readonly array $rentaNat = [],
        public readonly array $chargedRentaNat = [],
        public readonly array $unpaidRentaNatArr = [],
        public readonly array $overpaidRentaNatArr = [],
        public readonly array $personalUse = [],
        public readonly bool $isDead = false,
        public readonly ?string $deadDate = null,
        public readonly bool $isHeritor = false,
        public readonly array $children = [],
        public readonly array $additionalData = []
    ) {}

    /**
     * Create DTO from array
     */
    public static function fromArray(array $data): self
    {
        return new self(
            ownerId: $data['owner_id'],
            ownerNames: $data['owner_names'] ?? '',
            path: $data['path'] ?? null,
            ownerPathKey: $data['owner_path_key'] ?? null,
            contractId: $data['contract_id'] ?? '',
            parentId: $data['parent_id'] ?? null,
            contractNumber: $data['c_num'] ?? '',
            farmingName: $data['farming_name'] ?? '',
            allOwnerArea: $data['all_owner_area'] ?? '0',
            ownerArea: $data['owner_area'] ?? '0',
            personalUseArea: $data['pu_area'] ?? '0',
            renta: $data['renta'] ?? '0',
            chargedRenta: $data['charged_renta'] ?? '0',
            paidRenta: $data['paid_renta'] ?? '0',
            unpaidRenta: $data['unpaid_renta'] ?? '0',
            overpaidRenta: $data['overpaid_renta'] ?? '0',
            rentaNat: $data['renta_nat'] ?? [],
            chargedRentaNat: $data['charged_renta_nat'] ?? [],
            unpaidRentaNatArr: $data['unpaid_renta_nat_arr'] ?? [],
            overpaidRentaNatArr: $data['overpaid_renta_nat_arr'] ?? [],
            personalUse: $data['personal_use'] ?? [],
            isDead: $data['is_dead'] ?? false,
            deadDate: $data['dead_date'] ?? null,
            isHeritor: $data['is_heritor'] ?? false,
            children: $data['children'] ?? [],
            additionalData: $data['additional_data'] ?? []
        );
    }

    /**
     * Convert to array
     */
    public function toArray(): array
    {
        return [
            'owner_id' => $this->ownerId,
            'owner_names' => $this->ownerNames,
            'path' => $this->path,
            'owner_path_key' => $this->ownerPathKey,
            'contract_id' => $this->contractId,
            'parent_id' => $this->parentId,
            'c_num' => $this->contractNumber,
            'farming_name' => $this->farmingName,
            'all_owner_area' => $this->allOwnerArea,
            'owner_area' => $this->ownerArea,
            'pu_area' => $this->personalUseArea,
            'renta' => $this->renta,
            'charged_renta' => $this->chargedRenta,
            'paid_renta' => $this->paidRenta,
            'unpaid_renta' => $this->unpaidRenta,
            'overpaid_renta' => $this->overpaidRenta,
            'renta_nat' => $this->rentaNat,
            'charged_renta_nat' => $this->chargedRentaNat,
            'unpaid_renta_nat_arr' => $this->unpaidRentaNatArr,
            'overpaid_renta_nat_arr' => $this->overpaidRentaNatArr,
            'personal_use' => $this->personalUse,
            'is_dead' => $this->isDead,
            'dead_date' => $this->deadDate,
            'is_heritor' => $this->isHeritor,
            'children' => $this->children,
            'additional_data' => $this->additionalData,
        ];
    }

    /**
     * Create a new instance with updated data
     */
    public function with(array $updates): self
    {
        $data = $this->toArray();
        $data = array_merge($data, $updates);
        
        return self::fromArray($data);
    }
}
