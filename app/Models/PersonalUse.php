<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * PersonalUse Model
 * 
 * Represents personal use areas for owners in agricultural contracts
 */
class PersonalUse extends Model
{
    protected $table = 'personal_use';

    protected $fillable = [
        'owner_id',
        'contract_id',
        'year',
        'renta_type',
        'personal_use_unit_value',
        'personal_use_renta',
        'personal_use_paid_renta',
        'personal_use_unpaid_renta',
        'personal_use_treatments_sum',
        'personal_use_paid_treatments',
        'personal_use_unpaid_treatments',
        'renta_type_name',
    ];

    protected $casts = [
        'personal_use_unit_value' => 'decimal:6',
        'personal_use_renta' => 'decimal:6',
        'personal_use_paid_renta' => 'decimal:6',
        'personal_use_unpaid_renta' => 'decimal:6',
        'personal_use_treatments_sum' => 'decimal:6',
        'personal_use_paid_treatments' => 'decimal:6',
        'personal_use_unpaid_treatments' => 'decimal:6',
    ];

    /**
     * Get the owner this personal use belongs to
     */
    public function owner(): BelongsTo
    {
        return $this->belongsTo(Owner::class);
    }

    /**
     * Get the contract this personal use is associated with
     */
    public function contract(): BelongsTo
    {
        return $this->belongsTo(Contract::class);
    }

    /**
     * Get the rent type for this personal use
     */
    public function rentType(): BelongsTo
    {
        return $this->belongsTo(RentType::class, 'renta_type');
    }

    /**
     * Scope to filter by farming year
     */
    public function scopeForYear($query, int $year)
    {
        return $query->where('year', $year);
    }

    /**
     * Scope to filter by owner
     */
    public function scopeForOwner($query, int $ownerId)
    {
        return $query->where('owner_id', $ownerId);
    }

    /**
     * Scope to filter by contract
     */
    public function scopeForContract($query, int $contractId)
    {
        return $query->where('contract_id', $contractId);
    }

    /**
     * Get the total personal use renta amount
     */
    public function getTotalRentaAttribute(): string
    {
        return (string) $this->personal_use_renta;
    }

    /**
     * Get the remaining unpaid renta amount
     */
    public function getRemainingRentaAttribute(): string
    {
        return (string) $this->personal_use_unpaid_renta;
    }

    /**
     * Check if personal use is fully paid
     */
    public function isFullyPaid(): bool
    {
        return bccomp((string) $this->personal_use_unpaid_renta, '0', 6) <= 0;
    }
}
