<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Owner Model
 * 
 * Represents property owners in the agricultural rent management system
 */
class Owner extends Model
{
    protected $table = 'owners';

    protected $fillable = [
        'owner_names',
        'egn_eik',
        'owner_type',
        'phone',
        'mobile',
        'address',
        'company_address',
        'lk_nomer',
        'lk_izdavane',
        'rent_place',
        'iban',
        'is_dead',
        'dead_date',
        'allow_owner_payment',
        'post_payment_fields',
        'parent_id',
    ];

    protected $casts = [
        'is_dead' => 'boolean',
        'allow_owner_payment' => 'boolean',
        'dead_date' => 'date',
        'lk_izdavane' => 'date',
        'post_payment_fields' => 'array',
    ];

    /**
     * Get the contracts associated with this owner
     */
    public function contracts(): BelongsToMany
    {
        return $this->belongsToMany(Contract::class, 'plot_contracts', 'owner_id', 'contract_id')
            ->withPivot(['path', 'plots_percent', 'plot_owned_area', 'plot_owned_area_total']);
    }

    /**
     * Get the plots owned by this owner
     */
    public function plots(): BelongsToMany
    {
        return $this->belongsToMany(Plot::class, 'plot_contracts', 'owner_id', 'plot_id')
            ->withPivot(['contract_id', 'path', 'plots_percent', 'plot_owned_area']);
    }

    /**
     * Get the payments made by this owner
     */
    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class, 'owner_id');
    }

    /**
     * Get the personal use records for this owner
     */
    public function personalUse(): HasMany
    {
        return $this->hasMany(PersonalUse::class, 'owner_id');
    }

    /**
     * Get the parent owner (for inheritance)
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(Owner::class, 'parent_id');
    }

    /**
     * Get the child owners (heirs)
     */
    public function children(): HasMany
    {
        return $this->hasMany(Owner::class, 'parent_id');
    }

    /**
     * Scope to get only living owners
     */
    public function scopeAlive($query)
    {
        return $query->where('is_dead', false);
    }

    /**
     * Scope to get only deceased owners
     */
    public function scopeDeceased($query)
    {
        return $query->where('is_dead', true);
    }

    /**
     * Scope to filter by farming year for death status
     */
    public function scopeDeadInFarmingYear($query, int $year)
    {
        $farmYearStart = ($year - 1) . '-10-01';
        $farmYearEnd = $year . '-09-30';

        return $query->where('is_dead', true)
            ->whereBetween('dead_date', [$farmYearStart, $farmYearEnd]);
    }

    /**
     * Check if owner is dead in specific farming year
     */
    public function isDeadInFarmingYear(int $year): bool
    {
        if (!$this->is_dead || !$this->dead_date) {
            return false;
        }

        $farmYearStart = ($year - 1) . '-10-01';
        $farmYearEnd = $year . '-09-30';

        return $this->dead_date >= $farmYearStart && $this->dead_date <= $farmYearEnd;
    }

    /**
     * Get formatted owner name based on type
     */
    public function getFormattedNameAttribute(): string
    {
        return $this->owner_names ?? '';
    }

    /**
     * Get formatted address based on owner type
     */
    public function getFormattedAddressAttribute(): string
    {
        return $this->owner_type === 1 ? $this->address : $this->company_address;
    }

    /**
     * Check if owner is a company
     */
    public function isCompany(): bool
    {
        return $this->owner_type !== 1;
    }

    /**
     * Check if owner is an individual
     */
    public function isIndividual(): bool
    {
        return $this->owner_type === 1;
    }
}
