<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Plot Model
 * 
 * Represents agricultural plots/parcels in the rent management system
 */
class Plot extends Model
{
    protected $table = 'plots';

    protected $fillable = [
        'kad_ident',
        'mestnost',
        'virtual_category_title',
        'virtual_ekatte_name',
        'virtual_ntp_title',
        'ekate',
        'cultivated_area',
        'total_area',
    ];

    protected $casts = [
        'cultivated_area' => 'decimal:6',
        'total_area' => 'decimal:6',
    ];

    /**
     * Get the owners associated with this plot
     */
    public function owners(): BelongsToMany
    {
        return $this->belongsToMany(Owner::class, 'plot_contracts', 'plot_id', 'owner_id')
            ->withPivot(['contract_id', 'path', 'plots_percent', 'plot_owned_area']);
    }

    /**
     * Get the contracts associated with this plot
     */
    public function contracts(): BelongsToMany
    {
        return $this->belongsToMany(Contract::class, 'plot_contracts', 'plot_id', 'contract_id')
            ->withPivot(['owner_id', 'path', 'plots_percent', 'plot_owned_area']);
    }

    /**
     * Get the rent types for this plot
     */
    public function rentTypes(): HasMany
    {
        return $this->hasMany(PlotRentType::class, 'plot_id');
    }

    /**
     * Get the charged rents for this plot
     */
    public function chargedRents(): HasMany
    {
        return $this->hasMany(ChargedRent::class, 'plot_id');
    }

    /**
     * Scope to filter by EKATTE code
     */
    public function scopeByEkatte($query, array $ekatteCodes)
    {
        return $query->whereIn('ekate', $ekatteCodes);
    }

    /**
     * Get formatted location name
     */
    public function getFormattedLocationAttribute(): string
    {
        $parts = array_filter([
            $this->virtual_ekatte_name,
            $this->mestnost,
        ]);

        return implode(', ', $parts);
    }

    /**
     * Get formatted area type and category
     */
    public function getFormattedTypeAttribute(): string
    {
        $parts = array_filter([
            $this->virtual_ntp_title,
            $this->virtual_category_title,
        ]);

        return implode(' - ', $parts);
    }
}
