<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Contract Model
 * 
 * Represents agricultural contracts in the rent management system
 */
class Contract extends Model
{
    protected $table = 'contracts';

    protected $fillable = [
        'c_num',
        'parent_id',
        'farming_id',
        'farming_name',
        'nm_usage_rights',
        'virtual_contract_type',
        'start_date',
        'due_date',
        'osz_num',
        'osz_date',
        'sv_num',
        'sv_date',
        'rent_money_value',
        'rent_nature_json',
        'contract_group_name',
    ];

    protected $casts = [
        'start_date' => 'date',
        'due_date' => 'date',
        'osz_date' => 'date',
        'sv_date' => 'date',
        'rent_money_value' => 'decimal:6',
        'rent_nature_json' => 'array',
    ];

    /**
     * Get the parent contract (for annexes)
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(Contract::class, 'parent_id');
    }

    /**
     * Get the child contracts (annexes)
     */
    public function annexes(): HasMany
    {
        return $this->hasMany(Contract::class, 'parent_id');
    }

    /**
     * Get the owners associated with this contract
     */
    public function owners(): BelongsToMany
    {
        return $this->belongsToMany(Owner::class, 'plot_contracts', 'contract_id', 'owner_id')
            ->withPivot(['path', 'plots_percent', 'plot_owned_area', 'plot_owned_area_total']);
    }

    /**
     * Get the plots associated with this contract
     */
    public function plots(): BelongsToMany
    {
        return $this->belongsToMany(Plot::class, 'plot_contracts', 'contract_id', 'plot_id')
            ->withPivot(['owner_id', 'path', 'plots_percent', 'plot_owned_area']);
    }

    /**
     * Get the payments for this contract
     */
    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class, 'contract_id');
    }

    /**
     * Get the rent types associated with this contract
     */
    public function rentTypes(): BelongsToMany
    {
        return $this->belongsToMany(RentType::class, 'contract_rent_types', 'contract_id', 'rent_type_id')
            ->withPivot(['nat_value', 'unit_value', 'unit_id']);
    }

    /**
     * Scope to get main contracts (not annexes)
     */
    public function scopeMainContracts($query)
    {
        return $query->whereNull('parent_id');
    }

    /**
     * Scope to get annexes only
     */
    public function scopeAnnexes($query)
    {
        return $query->whereNotNull('parent_id');
    }

    /**
     * Scope to filter by farming year
     */
    public function scopeForFarmingYear($query, int $year)
    {
        $farmYearStart = ($year - 1) . '-10-01';
        $farmYearEnd = $year . '-09-30';

        return $query->where(function ($q) use ($farmYearStart, $farmYearEnd) {
            $q->whereBetween('start_date', [$farmYearStart, $farmYearEnd])
              ->orWhereBetween('due_date', [$farmYearStart, $farmYearEnd])
              ->orWhere(function ($q2) use ($farmYearStart, $farmYearEnd) {
                  $q2->where('start_date', '<=', $farmYearStart)
                     ->where('due_date', '>=', $farmYearEnd);
              });
        });
    }

    /**
     * Check if contract is an annex
     */
    public function isAnnex(): bool
    {
        return !is_null($this->parent_id);
    }

    /**
     * Check if contract is a main contract
     */
    public function isMainContract(): bool
    {
        return is_null($this->parent_id);
    }

    /**
     * Get the effective contract ID (parent ID if annex, own ID if main)
     */
    public function getEffectiveContractIdAttribute(): int
    {
        return $this->parent_id ?? $this->id;
    }

    /**
     * Get formatted contract number with group name
     */
    public function getFormattedContractNumberAttribute(): string
    {
        if ($this->contract_group_name) {
            return $this->c_num . ' (' . $this->contract_group_name . ')';
        }

        return $this->c_num;
    }
}
