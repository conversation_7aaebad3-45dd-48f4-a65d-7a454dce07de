<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

/**
 * RentType Model
 * 
 * Represents different types of rent (money, nature) in the agricultural system
 */
class RentType extends Model
{
    protected $table = 'rent_types';

    protected $fillable = [
        'name',
        'type',
        'unit_id',
        'unit_name',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * Get the contracts that use this rent type
     */
    public function contracts(): BelongsToMany
    {
        return $this->belongsToMany(Contract::class, 'contract_rent_types', 'rent_type_id', 'contract_id')
            ->withPivot(['nat_value', 'unit_value', 'unit_id']);
    }

    /**
     * Get the payments for this rent type
     */
    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * Get the personal use records for this rent type
     */
    public function personalUse(): HasMany
    {
        return $this->hasMany(PersonalUse::class, 'renta_type');
    }

    /**
     * Scope to get active rent types
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get rent types by type
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope to get money rent types
     */
    public function scopeMoneyTypes($query)
    {
        return $query->where('type', 'money');
    }

    /**
     * Scope to get nature rent types
     */
    public function scopeNatureTypes($query)
    {
        return $query->where('type', 'nature');
    }

    /**
     * Check if rent type is for money
     */
    public function isMoneyType(): bool
    {
        return $this->type === 'money';
    }

    /**
     * Check if rent type is for nature
     */
    public function isNatureType(): bool
    {
        return $this->type === 'nature';
    }

    /**
     * Get formatted name with unit
     */
    public function getFormattedNameAttribute(): string
    {
        if ($this->unit_name) {
            return $this->name . ' (' . $this->unit_name . ')';
        }

        return $this->name;
    }
}
