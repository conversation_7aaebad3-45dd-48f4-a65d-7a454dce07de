<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Payment Model
 * 
 * Represents payments made by owners for agricultural rents
 */
class Payment extends Model
{
    protected $table = 'payments';

    protected $fillable = [
        'owner_id',
        'contract_id',
        'year',
        'payment_date',
        'amount',
        'payment_type',
        'rent_type_id',
        'nat_amount',
        'nat_unit_value',
        'is_converted',
        'payment_method',
        'reference_number',
        'notes',
    ];

    protected $casts = [
        'payment_date' => 'date',
        'amount' => 'decimal:6',
        'nat_amount' => 'decimal:6',
        'nat_unit_value' => 'decimal:6',
        'is_converted' => 'boolean',
    ];

    /**
     * Get the owner who made this payment
     */
    public function owner(): BelongsTo
    {
        return $this->belongsTo(Owner::class);
    }

    /**
     * Get the contract this payment is for
     */
    public function contract(): BelongsTo
    {
        return $this->belongsTo(Contract::class);
    }

    /**
     * Get the rent type this payment is for (if applicable)
     */
    public function rentType(): BelongsTo
    {
        return $this->belongsTo(RentType::class);
    }

    /**
     * Scope to filter by farming year
     */
    public function scopeForYear($query, int $year)
    {
        return $query->where('year', $year);
    }

    /**
     * Scope to filter by payment type
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('payment_type', $type);
    }

    /**
     * Scope to get money payments
     */
    public function scopeMoneyPayments($query)
    {
        return $query->where('payment_type', 'money');
    }

    /**
     * Scope to get nature payments
     */
    public function scopeNaturePayments($query)
    {
        return $query->where('payment_type', 'nature');
    }

    /**
     * Check if payment is in money
     */
    public function isMoneyPayment(): bool
    {
        return $this->payment_type === 'money';
    }

    /**
     * Check if payment is in nature
     */
    public function isNaturePayment(): bool
    {
        return $this->payment_type === 'nature';
    }

    /**
     * Get the effective amount (converted to money if needed)
     */
    public function getEffectiveAmountAttribute(): string
    {
        if ($this->isMoneyPayment()) {
            return (string) $this->amount;
        }

        if ($this->isNaturePayment() && $this->nat_unit_value) {
            return (string) ($this->nat_amount * $this->nat_unit_value);
        }

        return '0';
    }
}
