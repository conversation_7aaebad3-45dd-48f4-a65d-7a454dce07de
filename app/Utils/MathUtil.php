<?php

declare(strict_types=1);

namespace App\Utils;

/**
 * Precision Mathematics Utility Class
 * 
 * Provides high-precision mathematical operations using BC Math functions
 * for financial calculations in agricultural rent management.
 * 
 * Migrated from legacy TF\Engine\Kernel\Math class
 */
class MathUtil
{
    /**
     * Default scale for BC Math operations
     */
    public static int $scale = 15;

    /**
     * Add two arbitrary precision numbers
     *
     * @param string|int|float $addend1 First number to add
     * @param string|int|float $addend2 Second number to add
     * @param int $round Number of decimal places to round to (default: 6)
     * @return string Result of addition
     */
    public static function add(string|int|float $addend1, string|int|float $addend2, int $round = 6): string
    {
        $result = bcadd((string) $addend1, (string) $addend2, self::$scale);

        if ($round > 0) {
            $result = self::round($result, $round);
        }

        return $result;
    }

    /**
     * Subtract one arbitrary precision number from another
     *
     * @param string|int|float $minuend Number to subtract from
     * @param string|int|float $subtrahend Number to subtract
     * @param int $round Number of decimal places to round to (default: 6)
     * @return string Result of subtraction
     */
    public static function sub(string|int|float $minuend, string|int|float $subtrahend, int $round = 6): string
    {
        $result = bcsub((string) $minuend, (string) $subtrahend, self::$scale);

        if ($round > 0) {
            $result = self::round($result, $round);
        }

        return $result;
    }

    /**
     * Multiply two arbitrary precision numbers
     *
     * @param string|int|float $multiplicand First number to multiply
     * @param string|int|float $multiplier Second number to multiply
     * @param int $round Number of decimal places to round to (default: 6)
     * @return string Result of multiplication
     */
    public static function mul(string|int|float $multiplicand, string|int|float $multiplier, int $round = 6): string
    {
        $result = bcmul((string) $multiplicand, (string) $multiplier, self::$scale);

        return self::round($result, $round);
    }

    /**
     * Divide two arbitrary precision numbers
     *
     * @param string|int|float $dividend Number to divide
     * @param string|int|float $divisor Number to divide by
     * @param int $round Number of decimal places to round to (default: 6)
     * @return string Result of division
     * @throws \InvalidArgumentException If divisor is zero
     */
    public static function div(string|int|float $dividend, string|int|float $divisor, int $round = 6): string
    {
        if (bccomp((string) $divisor, '0', self::$scale) === 0) {
            throw new \InvalidArgumentException('Division by zero is not allowed');
        }

        $result = bcdiv((string) $dividend, (string) $divisor, self::$scale);

        return self::round($result, $round);
    }

    /**
     * Compare two arbitrary precision numbers
     *
     * @param string|int|float $num1 First number to compare
     * @param string|int|float $num2 Second number to compare
     * @return int 0 if equal, 1 if num1 > num2, -1 if num1 < num2
     */
    public static function compare(string|int|float $num1, string|int|float $num2): int
    {
        return bccomp((string) $num1, (string) $num2, self::$scale);
    }

    /**
     * Round a number to specified decimal places
     *
     * @param string|int|float $number Number to round
     * @param int $precision Number of decimal places
     * @return string Rounded number
     */
    public static function round(string|int|float $number, int $precision): string
    {
        $number = (string) $number;
        
        if ($precision < 0) {
            $precision = 0;
        }

        // Use bcadd with 0.5 * 10^(-precision) for proper rounding
        $roundingFactor = bcdiv('1', bcpow('10', (string) $precision, self::$scale), self::$scale);
        $roundingFactor = bcdiv($roundingFactor, '2', self::$scale);

        if (bccomp($number, '0', self::$scale) >= 0) {
            $rounded = bcadd($number, $roundingFactor, self::$scale);
        } else {
            $rounded = bcsub($number, $roundingFactor, self::$scale);
        }

        return bcadd($rounded, '0', $precision);
    }

    /**
     * Get absolute value of a number
     *
     * @param string|int|float $number Input number
     * @return string Absolute value
     */
    public static function abs(string|int|float $number): string
    {
        $number = (string) $number;
        
        if (bccomp($number, '0', self::$scale) < 0) {
            return bcmul($number, '-1', self::$scale);
        }

        return $number;
    }

    /**
     * Check if a number is zero
     *
     * @param string|int|float $number Number to check
     * @return bool True if number is zero
     */
    public static function isZero(string|int|float $number): bool
    {
        return bccomp((string) $number, '0', self::$scale) === 0;
    }

    /**
     * Check if a number is positive
     *
     * @param string|int|float $number Number to check
     * @return bool True if number is positive
     */
    public static function isPositive(string|int|float $number): bool
    {
        return bccomp((string) $number, '0', self::$scale) > 0;
    }

    /**
     * Check if a number is negative
     *
     * @param string|int|float $number Number to check
     * @return bool True if number is negative
     */
    public static function isNegative(string|int|float $number): bool
    {
        return bccomp((string) $number, '0', self::$scale) < 0;
    }

    /**
     * Get the maximum of two numbers
     *
     * @param string|int|float $num1 First number
     * @param string|int|float $num2 Second number
     * @return string Maximum value
     */
    public static function max(string|int|float $num1, string|int|float $num2): string
    {
        return bccomp((string) $num1, (string) $num2, self::$scale) >= 0 ? (string) $num1 : (string) $num2;
    }

    /**
     * Get the minimum of two numbers
     *
     * @param string|int|float $num1 First number
     * @param string|int|float $num2 Second number
     * @return string Minimum value
     */
    public static function min(string|int|float $num1, string|int|float $num2): string
    {
        return bccomp((string) $num1, (string) $num2, self::$scale) <= 0 ? (string) $num1 : (string) $num2;
    }

    /**
     * Calculate power of a number
     *
     * @param string|int|float $base Base number
     * @param string|int|float $exponent Exponent
     * @return string Result of base^exponent
     */
    public static function pow(string|int|float $base, string|int|float $exponent): string
    {
        return bcpow((string) $base, (string) $exponent, self::$scale);
    }

    /**
     * Sum an array of numbers
     *
     * @param array $numbers Array of numbers to sum
     * @param int $round Number of decimal places to round to (default: 6)
     * @return string Sum of all numbers
     */
    public static function sum(array $numbers, int $round = 6): string
    {
        $result = '0';
        
        foreach ($numbers as $number) {
            $result = bcadd($result, (string) $number, self::$scale);
        }

        return self::round($result, $round);
    }
}
