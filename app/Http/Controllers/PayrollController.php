<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\DTOs\PayrollRequestDTO;
use App\Services\PayrollService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Exception;

/**
 * Payroll Controller
 * 
 * API endpoints for owner payroll functionality
 * Migrated from legacy PaymentsController
 */
class PayrollController extends Controller
{
    public function __construct(
        private PayrollService $payrollService
    ) {}

    /**
     * Get owner payroll data
     * 
     * GET /api/payroll/owner
     * 
     * Migrated from getOwnerPayroll() method
     */
    public function getOwnerPayroll(Request $request): JsonResponse
    {
        try {
            $validated = $this->validateOwnerPayrollRequest($request);
            
            $requestDTO = PayrollRequestDTO::fromArray($validated);
            $response = $this->payrollService->getOwnerPayroll($requestDTO);
            
            return response()->json([
                'success' => true,
                'data' => $response->toArray(),
                'message' => 'Owner payroll data retrieved successfully'
            ]);
            
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
            
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve owner payroll data',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get owners payroll data (multiple owners)
     * 
     * GET /api/payroll/owners
     * 
     * Migrated from getOwnersPayroll() method
     */
    public function getOwnersPayroll(Request $request): JsonResponse
    {
        try {
            $validated = $this->validateOwnersPayrollRequest($request);
            
            $requestDTO = PayrollRequestDTO::fromArray($validated);
            $response = $this->payrollService->getOwnersPayroll($requestDTO);
            
            return response()->json([
                'success' => true,
                'data' => $response->toArray(),
                'message' => 'Owners payroll data retrieved successfully'
            ]);
            
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
            
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve owners payroll data',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get owner payments data
     * 
     * GET /api/payroll/owner/{ownerId}/payments
     */
    public function getOwnerPayments(Request $request, int $ownerId): JsonResponse
    {
        try {
            $validated = $request->validate([
                'year' => 'required|integer|min:2000|max:2100',
            ]);

            // This would use a separate service method for owner payments
            // For now, return a placeholder response
            
            return response()->json([
                'success' => true,
                'data' => [
                    'rows' => [],
                    'total' => 0,
                    'footer' => []
                ],
                'message' => 'Owner payments data retrieved successfully'
            ]);
            
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
            
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve owner payments data',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get contract payments data
     * 
     * GET /api/payroll/contract/{contractId}/payments
     */
    public function getContractPayments(Request $request, int $contractId): JsonResponse
    {
        try {
            $validated = $request->validate([
                'year' => 'required|integer|min:2000|max:2100',
                'annex_id' => 'nullable|integer',
                'page' => 'nullable|integer|min:1',
                'rows' => 'nullable|integer|min:1|max:1000',
                'sort' => 'nullable|string',
                'order' => 'nullable|string|in:asc,desc',
            ]);

            // This would use a separate service method for contract payments
            // For now, return a placeholder response
            
            return response()->json([
                'success' => true,
                'data' => [
                    'rows' => [],
                    'total' => 0,
                    'footer' => []
                ],
                'message' => 'Contract payments data retrieved successfully'
            ]);
            
        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
            
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve contract payments data',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Validate owner payroll request
     */
    private function validateOwnerPayrollRequest(Request $request): array
    {
        return $request->validate([
            'year' => 'required|integer|min:2000|max:2100',
            'owner_id' => 'nullable|integer',
            'path' => 'nullable|string',
            'filter_params' => 'nullable|array',
            'filter_params.payroll_ekate' => 'nullable|array',
            'filter_params.payroll_ekate.*' => 'string',
            'filter_params.payroll_farming' => 'nullable|array',
            'filter_params.payroll_farming.*' => 'nullable|integer',
            'filter_params.no_contract_payments' => 'nullable|boolean',
        ]);
    }

    /**
     * Validate owners payroll request
     */
    private function validateOwnersPayrollRequest(Request $request): array
    {
        return $request->validate([
            'year' => 'required|integer|min:2000|max:2100',
            'filter_params' => 'nullable|array',
            'filter_params.payroll_ekate' => 'nullable|array',
            'filter_params.payroll_ekate.*' => 'string',
            'filter_params.payroll_farming' => 'nullable|array',
            'filter_params.payroll_farming.*' => 'nullable|integer',
            'filter_params.owner_ids' => 'nullable|array',
            'filter_params.owner_ids.*' => 'integer',
            'rows' => 'nullable|integer|min:1|max:1000',
            'page' => 'nullable|integer|min:1',
            'return_flat_owners' => 'nullable|boolean',
        ]);
    }
}
