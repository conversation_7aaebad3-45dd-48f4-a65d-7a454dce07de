<?php

declare(strict_types=1);

namespace App\Services;

use App\DTOs\PayrollRequestDTO;
use App\DTOs\PayrollResponseDTO;
use App\DTOs\OwnerPayrollDTO;
use App\Repositories\PaymentRepository;
use App\Repositories\OwnerRepository;
use App\Repositories\ContractRepository;
use App\Utils\MathUtil;
use Exception;

/**
 * Payroll Service
 * 
 * Main service for owner payroll calculations
 * Migrated from legacy PaymentsController::getOwnerPayroll() functionality
 */
class PayrollService
{
    public function __construct(
        private PaymentRepository $paymentRepository,
        private OwnerRepository $ownerRepository,
        private ContractRepository $contractRepository,
        private PaymentCalculationService $paymentCalculationService,
        private PersonalUseService $personalUseService,
        private RentCalculationService $rentCalculationService,
        private FormattingService $formattingService
    ) {}

    /**
     * Get owner payroll data
     * 
     * Main method that orchestrates the payroll calculation process
     * Migrated from getOwnerPayroll() method
     */
    public function getOwnerPayroll(PayrollRequestDTO $request): PayrollResponseDTO
    {
        // Get payment plots data
        $plots = $this->getPaymentsPlots($request);

        // Process personal use areas
        $plots = $this->personalUseService->processOwnerPersonalUse($plots);

        // Aggregate plots by rent type and contract
        $aggPlots = $this->paymentCalculationService->aggPlots($plots, 'rent_type');
        $aggPlotsByContract = $this->paymentCalculationService->aggPlots($plots, 'contract');

        // Map contract payments if not disabled
        if (!isset($request->filterParams['no_contract_payments'])) {
            $aggPlots = $this->contractsPaymentsMapping(
                $aggPlots,
                $aggPlotsByContract,
                $request->year,
                $request->ownerId,
                $request->path
            );
        }

        // Format data for response
        $formattedData = $this->formattingService->formattingOwnersData($aggPlots);

        return new PayrollResponseDTO(
            rows: $formattedData,
            total: count($formattedData),
            footer: []
        );
    }

    /**
     * Get owners payroll data (multiple owners)
     * 
     * Migrated from getOwnersPayroll() method
     */
    public function getOwnersPayroll(PayrollRequestDTO $request): PayrollResponseDTO
    {
        $result = [];

        // Get allowed farming IDs (this would come from user context in real implementation)
        $allowedFarmingIds = $this->getAllowedFarmingIds();

        if (empty($allowedFarmingIds)) {
            throw new Exception('User has no allowed farmings');
        }

        // Apply farming filter
        $filterParams = $request->filterParams;
        $filterParams['payroll_farming'] = !empty($filterParams['payroll_farming']) && $filterParams['payroll_farming'][0] !== ''
            ? array_intersect($filterParams['payroll_farming'], $allowedFarmingIds)
            : $allowedFarmingIds;

        // Get owner contracts
        $ownerContracts = $this->paymentRepository->getOwnerContracts($request->year, $filterParams);

        // Extract all owner IDs
        $allOwnerIdsString = implode(',', array_column($ownerContracts, 'owner_ids'));
        $allOwnerIdsArray = array_unique(explode(',', $allOwnerIdsString));

        // Apply pagination if requested
        if ($request->rows && $request->page) {
            $start = ($request->page - 1) * $request->rows;
            $limitedOwners = array_slice($allOwnerIdsArray, $start, $request->rows);

            // Filter contracts to only include those with owners from current page
            $ownerContracts = array_filter($ownerContracts, function ($item) use ($limitedOwners) {
                $itemOwnerIds = explode(',', $item['owner_ids']);
                return count(array_intersect($itemOwnerIds, $limitedOwners)) > 0;
            });
        }

        $flatOwners = [];

        // Process each contract
        foreach ($ownerContracts as $contract) {
            $contractId = $contract['contract_id'];
            $annexId = null;

            if ($contract['parent_id']) {
                $contractId = $contract['parent_id'];
                $annexId = $contract['contract_id'];
            }

            // Get payments for the contract
            $contractsPayments = $this->getPayments($request->year, $contractId, $annexId, null, $filterParams);

            // Filter owners by contract owner IDs
            $filterParams['owner_ids'] = explode(',', $contract['owner_ids']);
            $filteredOwners = $this->filterOwnersTree($contractsPayments, $filterParams);

            // Flatten owners tree for aggregation
            $flatOwners[$contractId] = $this->flattenOwnersTree($filteredOwners);
        }

        // Return flat owners if requested
        if ($request->returnFlatOwners) {
            return new PayrollResponseDTO(
                rows: $this->formattingService->formattingOwnersData($flatOwners),
                total: count($flatOwners),
                footer: []
            );
        }

        // Aggregate data from all contracts
        foreach ($flatOwners as $contractId => $contractOwners) {
            foreach ($contractOwners as $ownerPath => $owner) {
                $result[$ownerPath] = $this->calculateOwnerData($result[$ownerPath] ?? null, $owner);
            }
        }

        // Build owners tree
        $ownersTree = $this->buildOwnersTree($result);

        return new PayrollResponseDTO(
            rows: $this->formattingService->formattingOwnersData($ownersTree),
            total: count($allOwnerIdsArray),
            footer: [$this->generateFooter($ownersTree, ['timespan'])]
        );
    }

    /**
     * Get payments plots data
     * 
     * Migrated from getPaymentsPlots() method
     */
    private function getPaymentsPlots(PayrollRequestDTO $request): array
    {
        return $this->paymentRepository->getPaymentPlots(
            $request->year,
            null,
            null,
            $request->ownerId,
            $request->path,
            $request->filterParams
        );
    }

    /**
     * Map contract payments to aggregated plots
     * 
     * Migrated from contractsPaymentsMapping() method
     */
    private function contractsPaymentsMapping(
        array $aggPlots,
        array $aggPlotsByContract,
        int $year,
        ?int $ownerId = null,
        ?string $path = null
    ): array {
        foreach ($aggPlotsByContract as $contract) {
            $contractId = $contract['contract_id'];
            $annexId = null;

            if ($contract['parent_id']) {
                $contractId = $contract['parent_id'];
                $annexId = $contract['contract_id'];
            }

            $contractPayments = $this->getPayments($year, $contractId, $annexId);
            $ownerPayment = null;

            if ($contractPayments) {
                $ownerPayment = $this->getOwnerDataFromContract($contractPayments, $path ?? $ownerId);
            }

            // Map payment data to aggregated plots
            foreach ($aggPlots as $plotKey => $plot) {
                if ($plot['contract_id'] == $contract['contract_id'] && $plot['parent_id'] == $contract['parent_id']) {
                    $aggPlots[$plotKey] = array_merge($aggPlots[$plotKey], [
                        'paid_renta' => $ownerPayment['paid_renta'] ?? '0',
                        'unpaid_renta' => $ownerPayment['unpaid_renta'] ?? '0',
                        'overpaid_renta' => $ownerPayment['overpaid_renta'] ?? '0',
                        'paid_renta_by' => $ownerPayment['paid_renta_by'] ?? '',
                        'paid_nat_via_money' => $ownerPayment['paid_nat_via_money'] ?? '0',
                        'paid_nat_via_nat' => $ownerPayment['paid_nat_via_nat'] ?? '0',
                        'paid_renta_nat_sum' => $ownerPayment['paid_renta_nat_sum'] ?? '0',
                        'overpaid_renta_nat_money_arr' => $ownerPayment['overpaid_renta_nat_money_arr'] ?? [],
                        'unpaid_renta_nat_money_arr' => $ownerPayment['unpaid_renta_nat_money_arr'] ?? [],
                        'overpaid_renta_nat_arr' => $ownerPayment['overpaid_renta_nat_arr'] ?? [],
                        'unpaid_renta_nat_arr' => $ownerPayment['unpaid_renta_nat_arr'] ?? [],
                        'paid_via_nat' => $ownerPayment['paid_via_nat'] ?? '0',
                        'paid_via_money' => $ownerPayment['paid_via_money'] ?? '0',
                        'rent_place' => $this->getRentPlace($aggPlots[$plotKey], $ownerPayment),
                    ]);
                }
            }
        }

        return $aggPlots;
    }

    /**
     * Get rent place name
     */
    private function getRentPlace(array $plot, ?array $ownerPayment): string
    {
        $rentPlace = $plot['rent_place'] ?? '';
        
        if (strlen($rentPlace) > 0 && $rentPlace !== '-') {
            return $rentPlace;
        }

        return $ownerPayment['rent_place_name'] ?? '';
    }

    /**
     * Placeholder for getAllowedFarmingIds - would be implemented based on user context
     */
    private function getAllowedFarmingIds(): array
    {
        // This would be implemented based on the user's permissions
        // For now, return empty array to trigger the exception
        return [];
    }

    /**
     * Placeholder methods that would need full implementation
     */
    private function getPayments(int $year, int $contractId, ?int $annexId, ?string $gridType = null, array $filterParams = []): array
    {
        // This would implement the full getPayments logic from the legacy system
        return [];
    }

    private function filterOwnersTree(array $payments, array $filterParams): array
    {
        // This would implement owner tree filtering logic
        return [];
    }

    private function flattenOwnersTree(array $ownersTree): array
    {
        // This would implement tree flattening logic
        return [];
    }

    private function calculateOwnerData(?array $existing, array $new): array
    {
        // This would implement owner data calculation/aggregation logic
        return $new;
    }

    private function buildOwnersTree(array $flatOwners): array
    {
        // This would implement owner tree building logic
        return [];
    }

    private function getOwnerDataFromContract(array $contractPayments, $ownerIdentifier): ?array
    {
        // This would implement owner data extraction from contract payments
        return null;
    }

    private function generateFooter(array $ownersTree, array $excludeFields = []): array
    {
        // This would implement footer generation logic
        return [];
    }
}
