<?php

declare(strict_types=1);

namespace App\Services;

use App\Utils\MathUtil;

/**
 * Formatting Service
 * 
 * Handles data formatting for owner payroll display
 * Migrated from legacy PaymentsController formatting methods
 */
class FormattingService
{
    /**
     * Format owners data for display
     * 
     * Migrated from formattingOwnersData() method
     */
    public function formattingOwnersData(array $ownersData): array
    {
        $formattedData = [];

        foreach ($ownersData as $key => $owner) {
            $formattedOwner = $this->formatSingleOwner($owner);
            
            // Process children recursively if they exist
            if (!empty($owner['children'])) {
                $formattedOwner['children'] = $this->formattingOwnersData($owner['children']);
            }
            
            $formattedData[$key] = $formattedOwner;
        }

        return $formattedData;
    }

    /**
     * Format a single owner's data
     */
    private function formatSingleOwner(array $owner): array
    {
        return [
            'id' => $owner['id'] ?? null,
            'uuid' => $owner['uuid'] ?? null,
            'owner_id' => $owner['owner_id'],
            'owner_names' => $owner['owner_names'] ?? '',
            'owner_path_key' => $owner['owner_path_key'] ?? null,
            'path' => $owner['path'] ?? null,
            'contract_id' => $owner['contract_id'] ?? '',
            'parent_id' => $owner['parent_id'] ?? null,
            'c_num' => $owner['c_num'] ?? '',
            'c_num_with_group_name' => $owner['c_num_with_group_name'] ?? '',
            'contract_group_name' => $owner['contract_group_name'] ?? '',
            'farming_id' => $owner['farming_id'] ?? null,
            'farming_name' => $owner['farming_name'] ?? '',
            'contract_type' => $owner['contract_type'] ?? '',
            'contract_start_date' => $owner['contract_start_date'] ?? null,
            'contract_due_date' => $owner['contract_due_date'] ?? null,
            
            // Owner personal information
            'egn_eik' => $owner['egn_eik'] ?? '',
            'phone' => $owner['phone'] ?? '',
            'mobile' => $owner['mobile'] ?? '',
            'address' => $owner['address'] ?? '',
            'lk_nomer' => $owner['lk_nomer'] ?? '',
            'lk_izdavane' => $owner['lk_izdavane'] ?? null,
            'iban' => $owner['iban'] ?? '',
            'is_dead' => $owner['is_dead'] ?? false,
            'dead_date' => $owner['dead_date'] ?? null,
            'allow_owner_payment' => $owner['allow_owner_payment'] ?? false,
            'is_heritor' => $owner['is_heritor'] ?? false,
            
            // Plot information
            'plot_id' => $owner['plot_id'] ?? null,
            'kad_ident' => $owner['kad_ident'] ?? '',
            'kad_idents_array' => $owner['kad_idents_array'] ?? [],
            'ekatte_name' => $owner['ekatte_name'] ?? '',
            'area_type' => $owner['area_type'] ?? '',
            'mestnost' => $owner['mestnost'] ?? '',
            'category' => $owner['category'] ?? '',
            'pc_rel_id' => $owner['pc_rel_id'] ?? null,
            
            // Area calculations
            'all_owner_area' => $this->formatNumber($owner['all_owner_area'] ?? '0', 3),
            'all_owner_contract_area' => $this->formatNumber($owner['all_owner_contract_area'] ?? '0', 3),
            'owner_area' => $this->formatNumber($owner['owner_area'] ?? '0', 3),
            'pu_area' => $this->formatNumber($owner['pu_area'] ?? '0', 3),
            'cultivated_area' => $this->formatNumber($owner['cultivated_area'] ?? '0', 3),
            
            // Money calculations
            'renta' => $this->formatMoney($owner['renta'] ?? '0'),
            'charged_renta' => $this->formatMoney($owner['charged_renta'] ?? '0'),
            'paid_renta' => $this->formatMoney($owner['paid_renta'] ?? '0'),
            'unpaid_renta' => $this->formatMoney($owner['unpaid_renta'] ?? '0'),
            'overpaid_renta' => $this->formatMoney($owner['overpaid_renta'] ?? '0'),
            'paid_via_money' => $this->formatMoney($owner['paid_via_money'] ?? '0'),
            'paid_via_nat' => $this->formatMoney($owner['paid_via_nat'] ?? '0'),
            
            // Natural rent information
            'renta_nat' => $this->formatNaturalRentArray($owner['renta_nat'] ?? []),
            'charged_renta_nat' => $this->formatNaturalRentArray($owner['charged_renta_nat'] ?? []),
            'unpaid_renta_nat_arr' => $this->formatNaturalRentArray($owner['unpaid_renta_nat_arr'] ?? []),
            'overpaid_renta_nat_arr' => $this->formatNaturalRentArray($owner['overpaid_renta_nat_arr'] ?? []),
            'unpaid_renta_nat_money_arr' => $this->formatNaturalRentMoneyArray($owner['unpaid_renta_nat_money_arr'] ?? []),
            'overpaid_renta_nat_money_arr' => $this->formatNaturalRentMoneyArray($owner['overpaid_renta_nat_money_arr'] ?? []),
            'paid_nat_via_nat' => $this->formatNaturalRentArray($owner['paid_nat_via_nat'] ?? []),
            'paid_renta_nat_sum' => $this->formatNaturalRentArray($owner['paid_renta_nat_sum'] ?? []),
            'renta_nat_info' => $owner['renta_nat_info'] ?? [],
            
            // Personal use information
            'personal_use' => $owner['personal_use'] ?? [],
            'personal_use_nat_type_id' => $owner['personal_use_nat_type_id'] ?? [],
            'personal_use_nat_types_names_arr' => $owner['personal_use_nat_types_names_arr'] ?? [],
            'personal_use_renta_arr' => $this->formatMoneyArray($owner['personal_use_renta_arr'] ?? []),
            'personal_use_paid_renta_arr' => $this->formatMoneyArray($owner['personal_use_paid_renta_arr'] ?? []),
            'personal_use_unpaid_renta_arr' => $this->formatMoneyArray($owner['personal_use_unpaid_renta_arr'] ?? []),
            'personal_use_treatments_sum_arr' => $this->formatMoneyArray($owner['personal_use_treatments_sum_arr'] ?? []),
            'personal_use_paid_treatments_arr' => $this->formatMoneyArray($owner['personal_use_paid_treatments_arr'] ?? []),
            'personal_use_unpaid_treatments_arr' => $this->formatMoneyArray($owner['personal_use_unpaid_treatments_arr'] ?? []),
            
            // Representative information
            'rep_names_array' => $owner['rep_names_array'] ?? [],
            'rep_ibans_array' => $owner['rep_ibans_array'] ?? [],
            
            // Additional data
            'rent_place' => $owner['rent_place'] ?? '',
            'rent_per_plot_value' => $owner['rent_per_plot_value'] ?? null,
            'rent_money_value' => $this->formatMoney($owner['rent_money_value'] ?? '0'),
            'charged_renta_value' => $this->formatMoney($owner['charged_renta_value'] ?? '0'),
            'owner_post_payment_fields' => $owner['owner_post_payment_fields'] ?? [],
            'farm_post_payment_fields' => $owner['farm_post_payment_fields'] ?? [],
            
            // Timespan for calculations
            'timespan' => $this->calculateTimespan($owner),
        ];
    }

    /**
     * Format a number with specified precision
     */
    private function formatNumber(string $number, int $precision = 2): string
    {
        return MathUtil::round($number, $precision);
    }

    /**
     * Format money amount
     */
    private function formatMoney(string $amount): string
    {
        return MathUtil::round($amount, 2);
    }

    /**
     * Format array of money amounts
     */
    private function formatMoneyArray(array $amounts): array
    {
        $formatted = [];
        foreach ($amounts as $key => $amount) {
            $formatted[$key] = $this->formatMoney((string) $amount);
        }
        return $formatted;
    }

    /**
     * Format natural rent array
     */
    private function formatNaturalRentArray(array $rentArray): array
    {
        $formatted = [];
        foreach ($rentArray as $rentTypeId => $amount) {
            $formatted[$rentTypeId] = MathUtil::round((string) $amount, 3);
        }
        return $formatted;
    }

    /**
     * Format natural rent money array
     */
    private function formatNaturalRentMoneyArray(array $rentArray): array
    {
        $formatted = [];
        foreach ($rentArray as $rentTypeId => $amount) {
            $formatted[$rentTypeId] = $this->formatMoney((string) $amount);
        }
        return $formatted;
    }

    /**
     * Calculate timespan for the owner record
     */
    private function calculateTimespan(array $owner): string
    {
        // This would calculate the time period for which the calculations apply
        // Based on contract dates, death dates, etc.
        
        $startDate = $owner['contract_start_date'] ?? null;
        $endDate = $owner['contract_due_date'] ?? null;
        $deadDate = $owner['dead_date'] ?? null;
        
        if ($deadDate && $startDate) {
            $start = new \DateTime($startDate);
            $end = new \DateTime($deadDate);
            $interval = $start->diff($end);
            return $interval->days . ' days';
        }
        
        if ($startDate && $endDate) {
            $start = new \DateTime($startDate);
            $end = new \DateTime($endDate);
            $interval = $start->diff($end);
            return $interval->days . ' days';
        }
        
        return 'Full period';
    }

    /**
     * Convert BGN to EUR (placeholder for currency conversion)
     */
    public function BGNtoEURO(string $amount, float $rate = 1.95583): string
    {
        return MathUtil::div($amount, (string) $rate, 2);
    }

    /**
     * Generate summary footer data
     */
    public function generateFooter(array $ownersData, array $excludeFields = []): array
    {
        $footer = [
            'total_owners' => count($ownersData),
            'total_area' => '0',
            'total_renta' => '0',
            'total_paid_renta' => '0',
            'total_unpaid_renta' => '0',
            'total_overpaid_renta' => '0',
        ];

        foreach ($ownersData as $owner) {
            if (!in_array('area', $excludeFields)) {
                $footer['total_area'] = MathUtil::add($footer['total_area'], $owner['owner_area'] ?? '0');
            }
            
            if (!in_array('renta', $excludeFields)) {
                $footer['total_renta'] = MathUtil::add($footer['total_renta'], $owner['renta'] ?? '0');
                $footer['total_paid_renta'] = MathUtil::add($footer['total_paid_renta'], $owner['paid_renta'] ?? '0');
                $footer['total_unpaid_renta'] = MathUtil::add($footer['total_unpaid_renta'], $owner['unpaid_renta'] ?? '0');
                $footer['total_overpaid_renta'] = MathUtil::add($footer['total_overpaid_renta'], $owner['overpaid_renta'] ?? '0');
            }
        }

        // Format footer values
        $footer['total_area'] = $this->formatNumber($footer['total_area'], 3);
        $footer['total_renta'] = $this->formatMoney($footer['total_renta']);
        $footer['total_paid_renta'] = $this->formatMoney($footer['total_paid_renta']);
        $footer['total_unpaid_renta'] = $this->formatMoney($footer['total_unpaid_renta']);
        $footer['total_overpaid_renta'] = $this->formatMoney($footer['total_overpaid_renta']);

        return $footer;
    }
}
