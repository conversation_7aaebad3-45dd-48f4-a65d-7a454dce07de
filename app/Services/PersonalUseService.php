<?php

declare(strict_types=1);

namespace App\Services;

use App\Utils\MathUtil;
use App\Repositories\PaymentRepository;

/**
 * Personal Use Service
 * 
 * Handles personal use area calculations and processing
 * Migrated from legacy PaymentsController personal use methods
 */
class PersonalUseService
{
    private int $areaPrecision = 3;
    private int $moneyPrecision = 2;
    private array $personalUse = [];

    public function __construct(
        private PaymentRepository $paymentRepository
    ) {}

    /**
     * Process owner personal use data
     * 
     * Migrated from processOwnerPersonalUse() method
     */
    public function processOwnerPersonalUse(array $owners, ?string $gridType = null): array
    {
        $partCoef = '1';

        foreach ($owners as $ownerKey => $owner) {
            if (in_array($gridType, ['contract_payments'])) {
                $partCoef = MathUtil::div($owner['plot_owned_area'], $owner['plot_owned_area_total']);
            }

            // Initialize personal use arrays
            $owners[$ownerKey]['personal_use'] = [];
            $owners[$ownerKey]['personal_use_nat_type_id'] = [];
            $owners[$ownerKey]['personal_use_nat_types_names_arr'] = [];
            $owners[$ownerKey]['personal_use_amount_arr'] = [];
            $owners[$ownerKey]['personal_use_price_sum'] = [];
            $owners[$ownerKey]['personal_use_paid_arr'] = [];
            $owners[$ownerKey]['personal_use_unpaid_arr'] = [];
            $owners[$ownerKey]['personal_use_total'] = [];

            // Process personal use data for this owner
            foreach ($this->personalUse as $personalUseValue) {
                if ($personalUseValue['owner_id'] === $owners[$ownerKey]['owner_id']) {
                    $owners[$ownerKey]['personal_use_nat_type_id'][] = $personalUseValue['renta_type'];
                    $owners[$ownerKey]['personal_use_unit_value'][] = $personalUseValue['personal_use_unit_value'];
                    $owners[$ownerKey]['personal_use_nat_types_names_arr'][] = $personalUseValue['renta_type_name'];
                    
                    $owners[$ownerKey]['personal_use_renta_arr'][] = MathUtil::mul(
                        $personalUseValue['personal_use_renta'],
                        $partCoef,
                        $this->moneyPrecision
                    );
                    
                    $owners[$ownerKey]['personal_use_paid_renta_arr'][] = MathUtil::mul(
                        $personalUseValue['personal_use_paid_renta'],
                        $partCoef,
                        $this->moneyPrecision
                    );
                    
                    $owners[$ownerKey]['personal_use_unpaid_renta_arr'][] = MathUtil::mul(
                        $personalUseValue['personal_use_unpaid_renta'],
                        $partCoef,
                        $this->moneyPrecision
                    );
                    
                    $owners[$ownerKey]['personal_use_treatments_sum_arr'][] = MathUtil::mul(
                        $personalUseValue['personal_use_treatments_sum'],
                        $partCoef,
                        $this->moneyPrecision
                    );
                    
                    $owners[$ownerKey]['personal_use_paid_treatments_arr'][] = MathUtil::mul(
                        $personalUseValue['personal_use_paid_treatments'],
                        $partCoef,
                        $this->moneyPrecision
                    );
                    
                    $owners[$ownerKey]['personal_use_unpaid_treatments_arr'][] = MathUtil::mul(
                        $personalUseValue['personal_use_unpaid_treatments'],
                        $partCoef,
                        $this->moneyPrecision
                    );
                }
            }
        }

        return $owners;
    }

    /**
     * Get personal use area for a specific owner and plot
     * 
     * Migrated from getPersonalUseArea() method
     */
    public function getPersonalUseArea(array $personalUseData, int $ownerId, array $plotData): string
    {
        $totalArea = '0';

        foreach ($personalUseData as $personalUse) {
            if ($personalUse['owner_id'] === $ownerId) {
                // Calculate personal use area based on the plot data
                // This would involve complex calculations based on the plot's characteristics
                $area = $this->calculatePersonalUseAreaForPlot($personalUse, $plotData);
                $totalArea = MathUtil::add($totalArea, $area, $this->areaPrecision);
            }
        }

        return $totalArea;
    }

    /**
     * Get personal use data for owners based on criteria
     * 
     * Migrated from getPersonalUseForOwners() method
     */
    public function getPersonalUseForOwners(array $criteria, bool $includeDetails = false, bool $processData = true): array
    {
        $personalUseData = $this->paymentRepository->getPersonalUseForOwners($criteria);

        if ($processData) {
            $this->personalUse = $personalUseData;
        }

        return $personalUseData;
    }

    /**
     * Process personal use for deceased owners
     * 
     * This handles the complex logic for distributing personal use areas
     * from deceased owners to their heirs
     */
    public function processDeceasedOwnerPersonalUse(array $plots, int $year): array
    {
        $personalUseFromParent = [];

        foreach ($plots as $plotKey => $plot) {
            if ($plot['is_dead']) {
                $deadDate = $plot['dead_date'] ?? '2023-01-01';
                $deadFarmingYear = $this->getFarmingYearFromDate($deadDate);

                if ($deadFarmingYear['id'] == $year) {
                    // Owner died in current farming year
                    $plots[$plotKey]['pu_area'] = $this->getPersonalUseArea($this->personalUse, $plot['owner_id'], $plot);

                    // Distribute personal use to heirs
                    foreach ($plots as $herPlotKey => $herPlotData) {
                        if ($herPlotData['path']) {
                            $path = explode('.', $herPlotData['path']);
                            if (in_array($plot['owner_id'], $path) && $herPlotData['pc_rel_id'] == $plot['pc_rel_id']) {
                                $herPercent = MathUtil::div($herPlotData['plots_percent'], '100');
                                $personalUseFromParent[] = $herPlotData['owner_path_key'];
                                $plots[$herPlotKey]['pu_area'] = MathUtil::mul(
                                    $plots[$plotKey]['pu_area'],
                                    $herPercent,
                                    $this->areaPrecision
                                );
                            }
                        }
                    }
                } else {
                    // Owner died in previous years - aggregate heir personal use
                    foreach ($plots as $PUplot) {
                        if ($PUplot['path']) {
                            $path = explode('.', $PUplot['path']);
                            if (in_array($plot['owner_id'], $path) && $PUplot['pc_rel_id'] == $plot['pc_rel_id']) {
                                $heritorPuArea = $this->getPersonalUseArea($this->personalUse, $PUplot['owner_id'], $PUplot);
                                if (MathUtil::isPositive($heritorPuArea)) {
                                    $plots[$plotKey]['pu_area'] = MathUtil::add(
                                        $plots[$plotKey]['pu_area'],
                                        $heritorPuArea,
                                        $this->areaPrecision
                                    );
                                }
                            }
                        }
                    }
                }
            } else {
                // Living owner - calculate personal use if not inherited
                if (!in_array($plot['owner_path_key'], $personalUseFromParent)) {
                    $plots[$plotKey]['pu_area'] = $this->getPersonalUseArea($this->personalUse, $plot['owner_id'], $plot);
                }
            }
        }

        return $plots;
    }

    /**
     * Calculate personal use area for a specific plot
     */
    private function calculatePersonalUseAreaForPlot(array $personalUse, array $plotData): string
    {
        // This would implement the complex logic for calculating personal use area
        // based on the personal use record and plot characteristics
        
        // For now, return a basic calculation
        $baseArea = $personalUse['personal_use_unit_value'] ?? '0';
        $plotArea = $plotData['plot_owned_area'] ?? '0';
        
        // Apply some calculation logic here
        return MathUtil::min($baseArea, $plotArea);
    }

    /**
     * Get farming year from date
     * 
     * This would be implemented to match the legacy getFarmngYearFromDate() function
     */
    private function getFarmingYearFromDate(string $date): array
    {
        // Simplified implementation - would need to match legacy logic
        $year = (int) date('Y', strtotime($date));
        $month = (int) date('m', strtotime($date));
        
        // Farming year typically runs from October to September
        if ($month >= 10) {
            $farmingYear = $year + 1;
        } else {
            $farmingYear = $year;
        }
        
        return ['id' => $farmingYear];
    }
}
