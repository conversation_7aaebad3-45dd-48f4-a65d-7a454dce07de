<?php

declare(strict_types=1);

namespace App\Services;

use App\Utils\MathUtil;

/**
 * Rent Calculation Service
 * 
 * Handles rent calculations, natural rent processing, and rent type management
 * Migrated from legacy PaymentsController rent calculation methods
 */
class RentCalculationService
{
    private int $areaPrecision = 3;
    private int $moneyPrecision = 2;
    private int $rentaNatPrecision = 3;

    /**
     * Process rent calculations for plots
     * 
     * This method handles the complex rent calculation logic including
     * natural rents, charged rents, and individual plot rents
     */
    public function processPlotRentCalculations(array $plots, array $rentaNatArr): array
    {
        foreach ($plots as $plotKey => $plot) {
            // Calculate the area for rent calculations (total area minus personal use)
            $plots[$plotKey]['calculation_area'] = MathUtil::sub(
                $plots[$plotKey]['plot_owned_area'],
                $plots[$plotKey]['pu_area'],
                $this->areaPrecision
            );

            // Set area values
            $plots[$plotKey]['all_owner_area'] = MathUtil::round($plots[$plotKey]['plot_owned_area'], $this->areaPrecision);
            $plots[$plotKey]['all_owner_contract_area'] = MathUtil::round($plots[$plotKey]['plot_owned_contract_area'], $this->areaPrecision);
            $plots[$plotKey]['all_owner_no_rounded'] = $plots[$plotKey]['plot_owned_area'];
            $plots[$plotKey]['all_owner_no_rounded_contract'] = $plots[$plotKey]['plot_owned_contract_area'];
            $plots[$plotKey]['owner_area'] = MathUtil::round($plots[$plotKey]['calculation_area'], $this->areaPrecision);

            // Process natural rents
            $plots[$plotKey] = $this->processNaturalRents($plots[$plotKey], $rentaNatArr);

            // Calculate money rent
            $plots[$plotKey] = $this->calculateMoneyRent($plots[$plotKey]);

            // Initialize payment values
            $plots[$plotKey]['unpaid_renta'] = MathUtil::add(
                $plots[$plotKey]['contract_renta'] ?? '0',
                $plots[$plotKey]['charged_renta'] ?? '0'
            );
            $plots[$plotKey]['overpaid_renta'] = MathUtil::round('0', $this->moneyPrecision);
        }

        return $plots;
    }

    /**
     * Process natural rent calculations
     */
    private function processNaturalRents(array $plot, array $rentaNatArr): array
    {
        $chargedRentaNatArr = json_decode($plot['charged_renta_nat_json'] ?? '[]', true) ?: [];
        $rentaNatArrFromPlot = json_decode($plot['renta_nat_json'] ?? '[]', true) ?: [];
        $contractChargedRentaNat = [];

        // Initialize natural rent arrays
        $plot['renta_nat'] = [];
        $plot['charged_renta_nat'] = [];
        $plot['renta_nat_info'] = [];
        $plot['unpaid_renta_nat_arr'] = [];
        $plot['overpaid_renta_nat_arr'] = [];
        $plot['unpaid_renta_nat_money_arr'] = [];
        $plot['overpaid_renta_nat_money_arr'] = [];
        $plot['paid_nat_via_nat'] = [];
        $plot['paid_renta_nat_sum'] = [];
        $plot['charged_renta_nat_converted'] = '0';

        // Process each natural rent type
        foreach ($rentaNatArrFromPlot as $rentaNat) {
            $rentaNatId = $rentaNat['renta_nat_id'];
            
            $plot['renta_nat'][$rentaNatId] = '0';
            $plot['renta_nat_info'][$rentaNatId] = [
                'renta_nat_id' => $rentaNatId,
                'renta_nat_name' => $rentaNat['renta_nat_name'],
                'unit_id' => $rentaNat['unit_id'],
                'unit_name' => $rentaNat['unit_name'] ?? '',
                'nat_value' => $rentaNat['nat_value'],
                'unit_value' => $rentaNat['unit_value'],
                'charged_renta_nat' => null,
                'contract_renta_nat' => null,
            ];

            // Process charged natural rents
            if (!empty($chargedRentaNatArr)) {
                foreach ($chargedRentaNatArr as $chargedRentaNat) {
                    if ($plot['rent_per_plot_value'] > 0) {
                        continue; // Skip if individual rent is set
                    }

                    if ($chargedRentaNat['charged_renta_nat_id'] == $rentaNat['id']) {
                        if ($chargedRentaNat['nat_is_converted']) {
                            // Natural rent converted to money
                            $chargedRentNatValue = MathUtil::mul(
                                $chargedRentaNat['amount'],
                                $chargedRentaNat['unit_value']
                            );
                            $chargedRentaNatConverted = MathUtil::mul(
                                $chargedRentNatValue,
                                $plot['calculation_area']
                            );
                            $plot['charged_renta_nat_converted'] = MathUtil::add(
                                $plot['charged_renta_nat_converted'],
                                $chargedRentaNatConverted
                            );
                        } else {
                            // Natural rent in kind
                            $plot['charged_renta_nat'][$rentaNat['id']] = MathUtil::mul(
                                $chargedRentaNat['amount'],
                                $plot['calculation_area']
                            );
                            $plot['renta_nat_info'][$rentaNatId]['charged_renta_nat'] = MathUtil::mul(
                                $chargedRentaNat['amount'],
                                $plot['calculation_area']
                            );
                        }
                        $contractChargedRentaNat[] = $rentaNatId;
                    } else {
                        // Calculate contract natural rent
                        $rentNatValue = $plot['rent_per_plot_value'] > 0 ? '0' : $rentaNat['nat_value'];
                        $plot['renta_nat_info'][$rentaNatId]['contract_renta_nat'] = MathUtil::mul(
                            $rentNatValue,
                            $plot['calculation_area']
                        );
                        $plot['renta_nat'][$rentaNatId] = MathUtil::add(
                            $plot['renta_nat'][$rentaNatId],
                            $plot['renta_nat_info'][$rentaNatId]['contract_renta_nat']
                        );
                    }
                }
            } else {
                // No charged rents - use contract values
                $rentNatValue = $plot['rent_per_plot_value'] > 0 ? '0' : $rentaNat['nat_value'];
                $plot['renta_nat_info'][$rentaNatId]['contract_renta_nat'] = MathUtil::mul(
                    $rentNatValue,
                    $plot['calculation_area']
                );
                $plot['renta_nat'][$rentaNatId] = MathUtil::add(
                    $plot['renta_nat'][$rentaNatId],
                    $plot['renta_nat_info'][$rentaNatId]['contract_renta_nat']
                );
            }

            // Calculate unpaid natural rent
            $plotChargedRentaNat = $plot['renta_nat_info'][$rentaNatId]['charged_renta_nat'] ?? '0';
            $plotContractRentaNat = $plot['renta_nat_info'][$rentaNatId]['contract_renta_nat'] ?? '0';
            $unpaidRentaNat = MathUtil::add($plotChargedRentaNat, $plotContractRentaNat);
            
            $plot['unpaid_renta_nat_arr'][$rentaNatId] = $unpaidRentaNat;
            $plot['unpaid_renta_nat_money_arr'][$rentaNatId] = MathUtil::mul(
                $unpaidRentaNat,
                $rentaNat['unit_value']
            );

            // Initialize payment arrays
            $plot['overpaid_renta_nat_arr'][$rentaNatId] = MathUtil::round('0', $this->rentaNatPrecision);
            $plot['overpaid_renta_nat_money_arr'][$rentaNatId] = MathUtil::round('0', $this->moneyPrecision);
            $plot['paid_nat_via_nat'][$rentaNatId] = MathUtil::round('0', $this->rentaNatPrecision);
            $plot['paid_renta_nat_sum'][$rentaNatId] = MathUtil::round('0', $this->rentaNatPrecision);
        }

        // Process specific charged natural rents not in contract
        foreach ($chargedRentaNatArr as $chargedRentaNat) {
            if (in_array($chargedRentaNat['renta_nat_id'], $contractChargedRentaNat) || $plot['rent_per_plot_value'] > 0) {
                continue;
            }

            $rentNatUnitValue = '0';
            foreach ($rentaNatArr as $rentaNatInner) {
                if ($rentaNatInner['renta_nat_id'] == $chargedRentaNat['charged_renta_nat_id']) {
                    $rentNatUnitValue = $rentaNatInner['unit_value'];
                    break;
                }
            }

            $rentaNatId = $chargedRentaNat['charged_renta_nat_id'];
            $plot['renta_nat'][$rentaNatId] = '0';
            $plot['renta_nat_info'][$rentaNatId] = [
                'renta_nat_id' => $rentaNatId,
                'renta_nat_name' => $chargedRentaNat['charged_renta_nat_name'],
                'unit_id' => $chargedRentaNat['unit_id'],
                'unit_name' => $chargedRentaNat['unit_name'] ?? '',
                'nat_value' => null,
                'unit_value' => $rentNatUnitValue,
                'charged_renta_nat' => $chargedRentaNat['amount'],
                'contract_renta_nat' => null,
            ];

            if ($chargedRentaNat['nat_is_converted']) {
                $chargedRentNatValue = MathUtil::mul($chargedRentaNat['amount'], $chargedRentaNat['unit_value']);
                $chargedRentaNatConverted = MathUtil::mul($chargedRentNatValue, $plot['calculation_area']);
                $plot['charged_renta_nat_converted'] = MathUtil::add(
                    $plot['charged_renta_nat_converted'],
                    $chargedRentaNatConverted
                );
            } else {
                $plot['charged_renta_nat'][$rentaNatId] = MathUtil::mul(
                    $chargedRentaNat['amount'],
                    $plot['calculation_area']
                );
                $plot['renta_nat_info'][$rentaNatId]['charged_renta_nat'] = MathUtil::mul(
                    $chargedRentaNat['amount'],
                    $plot['calculation_area']
                );
            }
        }

        return $plot;
    }

    /**
     * Calculate money rent for a plot
     */
    private function calculateMoneyRent(array $plot): array
    {
        // Determine rent amount based on priority:
        // 1. Individual plot rent (highest priority)
        // 2. Charged rent + converted natural rent
        // 3. Contract rent (lowest priority)
        
        if (!is_null($plot['rent_per_plot']) && $plot['rent_per_plot'] > 0) {
            // Individual plot rent
            $plot['contract_renta'] = MathUtil::mul($plot['rent_per_plot'], $plot['calculation_area']);
            $plot['renta'] = MathUtil::mul($plot['rent_per_plot'], $plot['calculation_area']);
        } elseif (!is_null($plot['charged_renta_value']) || !is_null($plot['charged_renta_nat_converted'])) {
            // Charged rent (from billing) + converted natural rent
            $chargedRenta = MathUtil::mul($plot['charged_renta_value'] ?? '0', $plot['calculation_area']);
            $plot['charged_renta'] = MathUtil::add($chargedRenta, $plot['charged_renta_nat_converted'] ?? '0');
        } else {
            // Contract rent
            $plot['contract_renta'] = MathUtil::mul($plot['rent_money_value'], $plot['calculation_area']);
            $plot['renta'] = MathUtil::mul($plot['rent_money_value'], $plot['calculation_area']);
        }

        return $plot;
    }

    /**
     * Calculate tree rents for owner hierarchies
     * 
     * This handles the complex logic for distributing rents between
     * parents and children in inheritance scenarios
     */
    public function calculateTreeRents(array &$ownersTree): void
    {
        foreach ($ownersTree as &$owner) {
            if (!empty($owner['children'])) {
                $this->calculateChildrenRentsBasedOnParentPayments($owner);
                $this->calculateParentRentsBasedOnChildrenPayments($owner);
            }
        }
    }

    /**
     * Calculate children rents based on parent payments
     */
    private function calculateChildrenRentsBasedOnParentPayments(array &$owner): void
    {
        $parentPaidRenta = $owner['paid_renta'] ?? '0';
        
        if (MathUtil::isPositive($parentPaidRenta) && !empty($owner['children'])) {
            $totalChildrenRenta = '0';
            
            // Calculate total children renta
            foreach ($owner['children'] as $child) {
                $totalChildrenRenta = MathUtil::add($totalChildrenRenta, $child['unpaid_renta'] ?? '0');
            }
            
            // Distribute parent payment to children
            if (MathUtil::isPositive($totalChildrenRenta)) {
                $remainingPayment = $parentPaidRenta;
                
                foreach ($owner['children'] as &$child) {
                    $childRenta = $child['unpaid_renta'] ?? '0';
                    $childPayment = MathUtil::min($childRenta, $remainingPayment);
                    
                    $child['paid_renta'] = MathUtil::add($child['paid_renta'] ?? '0', $childPayment);
                    $child['unpaid_renta'] = MathUtil::sub($childRenta, $childPayment);
                    
                    $remainingPayment = MathUtil::sub($remainingPayment, $childPayment);
                    
                    if (MathUtil::isZero($remainingPayment)) {
                        break;
                    }
                }
            }
        }
    }

    /**
     * Calculate parent rents based on children payments
     */
    private function calculateParentRentsBasedOnChildrenPayments(array &$owner): void
    {
        if (empty($owner['children'])) {
            return;
        }
        
        $totalChildrenPayments = '0';
        
        // Sum all children payments
        foreach ($owner['children'] as $child) {
            $totalChildrenPayments = MathUtil::add($totalChildrenPayments, $child['paid_renta'] ?? '0');
        }
        
        // Apply children payments to parent's unpaid rent
        if (MathUtil::isPositive($totalChildrenPayments)) {
            $parentUnpaidRenta = $owner['unpaid_renta'] ?? '0';
            $parentPayment = MathUtil::min($parentUnpaidRenta, $totalChildrenPayments);
            
            $owner['paid_renta'] = MathUtil::add($owner['paid_renta'] ?? '0', $parentPayment);
            $owner['unpaid_renta'] = MathUtil::sub($parentUnpaidRenta, $parentPayment);
        }
    }
}
