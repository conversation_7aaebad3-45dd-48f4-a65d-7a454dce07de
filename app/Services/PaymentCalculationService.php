<?php

declare(strict_types=1);

namespace App\Services;

use App\Utils\MathUtil;

/**
 * Payment Calculation Service
 * 
 * Handles payment calculations, aggregations, and processing
 * Migrated from legacy PaymentsController payment calculation methods
 */
class PaymentCalculationService
{
    private int $areaPrecision = 3;
    private int $moneyPrecision = 2;
    private int $rentaNatPrecision = 3;
    private float $fixMoneyRoundingValue = 0.1;

    /**
     * Aggregate plots data by specified criteria
     * 
     * Migrated from aggPlots() method
     */
    public function aggPlots(array $plots, string $aggBy = 'owner'): array
    {
        $aggPlots = [];
        $aggPlotsByPlot = [];

        foreach ($plots as $key => $plot) {
            $aggKey = $this->getAggregationKey($plot, $aggBy);
            $aggKeyByPlot = $this->getPlotAggregationKey($plot, $aggBy, $aggKey);

            if (!isset($aggPlots[$aggKey])) {
                $aggPlots[$aggKey] = $this->initializeAggregatedPlot($plot, $key);
                
                if ($aggKeyByPlot && !in_array($aggKeyByPlot, $aggPlotsByPlot)) {
                    $aggPlotsByPlot[] = $aggKeyByPlot;
                }
            } else {
                $aggPlots[$aggKey] = $this->sumPayments(
                    $aggPlots[$aggKey],
                    $plot,
                    in_array($aggKeyByPlot, $aggPlotsByPlot)
                );

                if ($aggKeyByPlot && !in_array($aggKeyByPlot, $aggPlotsByPlot)) {
                    $aggPlotsByPlot[] = $aggKeyByPlot;
                }
            }

            // Handle plot-specific arrays
            $this->updatePlotArrays($aggPlots[$aggKey], $plot);
        }

        return $aggPlots;
    }

    /**
     * Sum payment data between two plot records
     * 
     * Migrated from sumPayments() method
     */
    public function sumPayments(array $payment1, array $payment2, bool $duplicatedPlot = false): array
    {
        // Add plot IDs
        $payment1['plot_id'][] = $payment2['plot_id'];
        $payment1['pc_rel_id'][] = $payment2['pc_rel_id'];

        // Sum area values
        $payment1['all_owner_area'] = MathUtil::add($payment1['all_owner_area'], $payment2['all_owner_area']);
        $payment1['all_owner_contract_area'] = MathUtil::add($payment1['all_owner_contract_area'], $payment2['all_owner_contract_area']);
        $payment1['all_owner_no_rounded'] = MathUtil::add($payment1['all_owner_no_rounded'], $payment2['all_owner_no_rounded']);
        $payment1['all_owner_no_rounded_contract'] = MathUtil::add($payment1['all_owner_no_rounded_contract'], $payment2['all_owner_no_rounded_contract']);
        $payment1['pu_area'] = MathUtil::add($payment1['pu_area'], $payment2['pu_area']);
        $payment1['owner_area'] = MathUtil::add($payment1['owner_area'], $payment2['calculation_area']);

        // Sum rent values
        $payment1['renta'] = MathUtil::add($payment1['renta'], $payment2['renta']);
        $payment1['charged_renta'] = MathUtil::add($payment1['charged_renta'], $payment2['charged_renta']);
        $payment1['unpaid_renta'] = MathUtil::add($payment1['unpaid_renta'], $payment2['unpaid_renta']);

        // Store plot-specific data
        $payment1['charged_renta_nat_values'][$payment2['plot_id']] = $payment2['charged_renta_nat'];
        $payment1['plots_percent'][$payment2['plot_id']] = $payment2['plots_percent'];

        // Sum natural rent values
        foreach ($payment2['renta_nat_info'] as $rentaNatInfo) {
            $rentaNatId = $rentaNatInfo['renta_nat_id'];
            
            $payment1['renta_nat'][$rentaNatId] = MathUtil::add(
                $payment1['renta_nat'][$rentaNatId] ?? '0',
                $rentaNatInfo['contract_renta_nat'] ?? '0'
            );
            
            $payment1['charged_renta_nat'][$rentaNatId] = MathUtil::add(
                $payment1['charged_renta_nat'][$rentaNatId] ?? '0',
                $rentaNatInfo['charged_renta_nat'] ?? '0'
            );
        }

        // Sum unpaid natural rent arrays
        foreach ($payment2['unpaid_renta_nat_arr'] as $rentaNatId => $value) {
            $payment1['unpaid_renta_nat_arr'][$rentaNatId] = MathUtil::add(
                $payment1['unpaid_renta_nat_arr'][$rentaNatId] ?? '0',
                $value
            );
        }

        foreach ($payment2['unpaid_renta_nat_money_arr'] as $rentaNatId => $value) {
            $payment1['unpaid_renta_nat_money_arr'][$rentaNatId] = MathUtil::add(
                $payment1['unpaid_renta_nat_money_arr'][$rentaNatId] ?? '0',
                $value
            );
        }

        // Sum cultivated area only if not duplicated plot
        if (!$duplicatedPlot) {
            $payment1['cultivated_area'] = MathUtil::add(
                $payment1['cultivated_area'],
                $payment2['cultivated_area']
            );
        }

        return $payment1;
    }

    /**
     * Fix rounding errors in payment calculations
     * 
     * Migrated from fixRounding() method
     */
    public function fixRounding(array $payments): array
    {
        foreach ($payments as $paymentKey => $payment) {
            $chargedRenta = $payment['charged_renta'] ?? '0';
            $renta = $payment['renta'] ?? '0';
            $paidRenta = $payment['paid_renta'] ?? '0';
            
            $sumRenta = MathUtil::add($chargedRenta, $renta);
            $rentCal = MathUtil::sub($sumRenta, $paidRenta);

            // Check for small positive rounding errors
            if (MathUtil::isPositive($rentCal) && MathUtil::compare($rentCal, (string) $this->fixMoneyRoundingValue) <= 0) {
                if (MathUtil::isPositive($chargedRenta)) {
                    $payments[$paymentKey]['charged_renta'] = MathUtil::sub($chargedRenta, $rentCal);
                } else {
                    $payments[$paymentKey]['renta'] = MathUtil::sub($renta, $rentCal);
                }
                $payments[$paymentKey]['unpaid_renta'] = MathUtil::round('0', $this->moneyPrecision);
            }

            // Check for small negative rounding errors
            if (MathUtil::isNegative($rentCal) && MathUtil::compare($rentCal, (string) (-$this->fixMoneyRoundingValue)) >= 0) {
                if (MathUtil::isPositive($chargedRenta)) {
                    $payments[$paymentKey]['charged_renta'] = MathUtil::sub($chargedRenta, $rentCal);
                } else {
                    $payments[$paymentKey]['renta'] = MathUtil::sub($renta, $rentCal);
                }
                $payments[$paymentKey]['unpaid_renta'] = MathUtil::round('0', $this->moneyPrecision);
            }
        }

        return $payments;
    }

    /**
     * Get aggregation key based on aggregation type
     */
    private function getAggregationKey(array $plot, string $aggBy): string
    {
        return match ($aggBy) {
            'owner' => $plot['owner_id'] . '_' . ($plot['path'] ?? '0'),
            'plot' => (string) $plot['pc_rel_id'],
            'contract' => (string) $plot['contract_id'],
            'rent_type' => $plot['owner_id'] . '_' . ($plot['plot_rent_type_id'] . '_' . $plot['pc_rel_id'] ?? '0'),
            default => $plot['owner_id'] . '_' . ($plot['path'] ?? '0'),
        };
    }

    /**
     * Get plot-specific aggregation key
     */
    private function getPlotAggregationKey(array $plot, string $aggBy, string $aggKey): ?string
    {
        if ($aggBy === 'owner') {
            return $aggKey . '_' . $plot['pc_rel_id'];
        }
        
        return null;
    }

    /**
     * Initialize aggregated plot data structure
     */
    private function initializeAggregatedPlot(array $plot, int $key): array
    {
        return [
            'uuid' => substr(md5($plot['owner_id'] . ($plot['path'] ?? '')), 0, 6),
            'contract_id' => $plot['contract_id'],
            'parent_id' => $plot['parent_id'],
            'c_num' => $plot['c_num'],
            'c_num_with_group_name' => $plot['c_num'] . (!empty($plot['contract_group_name']) ? ' (' . $plot['contract_group_name'] . ')' : ''),
            'contract_group_name' => $plot['contract_group_name'],
            'egn_eik' => $plot['egn_eik'],
            'farming_id' => $plot['farming_id'],
            'farming_name' => $plot['farming_name'],
            'contract_type' => $plot['contract_type'],
            'contract_start_date' => $plot['contract_start_date'],
            'contract_due_date' => $plot['contract_due_date'],
            'id' => $key + 1,
            'owner_id' => $plot['owner_id'],
            'owner_names' => $plot['owner_names'],
            'phone' => $plot['phone'],
            'mobile' => $plot['mobile'],
            'address' => $plot['address'],
            'lk_nomer' => $plot['lk_nomer'],
            'lk_izdavane' => $plot['lk_izdavane'],
            'iban' => $plot['iban'],
            'is_dead' => $plot['is_dead'],
            'dead_date' => $plot['dead_date'],
            'allow_owner_payment' => $plot['allow_owner_payment'],
            'plot_id' => $plot['plot_id'],
            'kad_ident' => $plot['kad_ident'],
            'ekatte_name' => $plot['ekatte_name'],
            'area_type' => $plot['area_type'],
            'mestnost' => $plot['mestnost'],
            'category' => $plot['category'],
            'pc_rel_id' => $plot['pc_rel_id'],
            'path' => $plot['path'],
            'all_owner_area' => $plot['all_owner_area'],
            'all_owner_contract_area' => $plot['all_owner_contract_area'],
            'all_owner_no_rounded' => $plot['all_owner_no_rounded'],
            'all_owner_no_rounded_contract' => $plot['all_owner_no_rounded_contract'],
            'plot_owned_area_total' => $plot['plot_owned_area_total'],
            'plot_owned_area' => $plot['plot_owned_area'],
            'pu_area' => $plot['pu_area'],
            'owner_area' => $plot['calculation_area'],
            'cultivated_area' => $plot['cultivated_area'],
            'renta' => $plot['renta'],
            'charged_renta' => $plot['charged_renta'],
            'paid_renta' => MathUtil::round('0', $this->moneyPrecision),
            'paid_via_money' => MathUtil::round('0', $this->moneyPrecision),
            'unpaid_renta' => $plot['unpaid_renta'],
            'overpaid_renta' => MathUtil::round('0', $this->moneyPrecision),
            'rent_per_plot_value' => $plot['rent_per_plot_value'],
            'rent_money_value' => $plot['rent_money_value'],
            'charged_renta_value' => $plot['charged_renta_value'],
            'renta_nat' => $plot['renta_nat'] ?? [],
            'charged_renta_nat' => $plot['charged_renta_nat'] ?? [],
            'renta_nat_info' => $plot['renta_nat_info'] ?? [],
            'unpaid_renta_nat_arr' => $plot['unpaid_renta_nat_arr'] ?? [],
            'overpaid_renta_nat_arr' => $plot['overpaid_renta_nat_arr'] ?? [],
            'unpaid_renta_nat_money_arr' => $plot['unpaid_renta_nat_money_arr'] ?? [],
            'overpaid_renta_nat_money_arr' => $plot['overpaid_renta_nat_money_arr'] ?? [],
            'kad_idents_array' => [],
            'rep_names_array' => [],
            'rep_ibans_array' => [],
        ];
    }

    /**
     * Update plot-specific arrays in aggregated data
     */
    private function updatePlotArrays(array &$aggPlot, array $plot): void
    {
        // Update kad_idents_array
        if (!in_array($plot['kad_ident'], $aggPlot['kad_idents_array'])) {
            $aggPlot['kad_idents_array'][] = $plot['kad_ident'];
        }

        // Update rep_names_array
        if (!empty($plot['rep_names']) && !in_array($plot['rep_names'], $aggPlot['rep_names_array'])) {
            $aggPlot['rep_names_array'][] = $plot['rep_names'];
        }

        // Update rep_ibans_array
        if (!empty($plot['rep_iban']) && !in_array($plot['rep_iban'], $aggPlot['rep_ibans_array'])) {
            $aggPlot['rep_ibans_array'][] = $plot['rep_iban'];
        }
    }
}
