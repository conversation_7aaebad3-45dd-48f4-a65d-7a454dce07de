<?php

declare(strict_types=1);

use App\Utils\MathUtil;

describe('MathUtil', function () {
    it('can add two numbers with precision', function () {
        $result = MathUtil::add('10.123', '5.456', 2);
        expect($result)->toBe('15.58');
    });

    it('can subtract two numbers with precision', function () {
        $result = MathUtil::sub('10.123', '5.456', 2);
        expect($result)->toBe('4.67');
    });

    it('can multiply two numbers with precision', function () {
        $result = MathUtil::mul('10.5', '2.5', 2);
        expect($result)->toBe('26.25');
    });

    it('can divide two numbers with precision', function () {
        $result = MathUtil::div('10.5', '2.5', 2);
        expect($result)->toBe('4.20');
    });

    it('throws exception when dividing by zero', function () {
        expect(fn() => MathUtil::div('10', '0'))
            ->toThrow(InvalidArgumentException::class, 'Division by zero');
    });

    it('can compare two numbers', function () {
        expect(MathUtil::compare('10.5', '5.2'))->toBe(1);
        expect(MathUtil::compare('5.2', '10.5'))->toBe(-1);
        expect(MathUtil::compare('10.5', '10.5'))->toBe(0);
    });

    it('can round numbers with precision', function () {
        expect(MathUtil::round('10.12345', 2))->toBe('10.12');
        expect(MathUtil::round('10.12678', 2))->toBe('10.13');
    });

    it('can get absolute value', function () {
        expect(MathUtil::abs('-10.5'))->toBe('10.5');
        expect(MathUtil::abs('10.5'))->toBe('10.5');
    });

    it('can check if number is zero', function () {
        expect(MathUtil::isZero('0'))->toBeTrue();
        expect(MathUtil::isZero('0.00'))->toBeTrue();
        expect(MathUtil::isZero('0.01'))->toBeFalse();
    });

    it('can check if number is positive', function () {
        expect(MathUtil::isPositive('10.5'))->toBeTrue();
        expect(MathUtil::isPositive('0'))->toBeFalse();
        expect(MathUtil::isPositive('-10.5'))->toBeFalse();
    });

    it('can check if number is negative', function () {
        expect(MathUtil::isNegative('-10.5'))->toBeTrue();
        expect(MathUtil::isNegative('0'))->toBeFalse();
        expect(MathUtil::isNegative('10.5'))->toBeFalse();
    });

    it('can find maximum of two numbers', function () {
        expect(MathUtil::max('10.5', '5.2'))->toBe('10.5');
        expect(MathUtil::max('5.2', '10.5'))->toBe('10.5');
    });

    it('can find minimum of two numbers', function () {
        expect(MathUtil::min('10.5', '5.2'))->toBe('5.2');
        expect(MathUtil::min('5.2', '10.5'))->toBe('5.2');
    });

    it('can sum array of numbers', function () {
        $numbers = ['10.5', '5.2', '3.3'];
        expect(MathUtil::sum($numbers, 1))->toBe('19.0');
    });

    it('returns zero for empty array sum', function () {
        expect(MathUtil::sum([]))->toBe('0');
    });

    it('handles precision correctly in all operations', function () {
        // Test with high precision numbers
        $a = '123.456789';
        $b = '987.654321';
        
        $sum = MathUtil::add($a, $b, 3);
        expect($sum)->toBe('1111.111');
        
        $diff = MathUtil::sub($b, $a, 3);
        expect($diff)->toBe('864.198');
        
        $product = MathUtil::mul($a, '2', 3);
        expect($product)->toBe('246.914');
        
        $quotient = MathUtil::div($a, '2', 3);
        expect($quotient)->toBe('61.728');
    });
});
