<?php

declare(strict_types=1);

use App\DTOs\PayrollRequestDTO;

describe('PayrollRequestDTO', function () {
    it('can be created from array with all parameters', function () {
        $data = [
            'year' => 2024,
            'owner_id' => 123,
            'path' => '1.2.3',
            'filter_params' => [
                'payroll_ekate' => ['Sofia', 'Plovdiv'],
                'payroll_farming' => [1, 2, null],
                'no_contract_payments' => true,
            ],
            'rows' => 50,
            'page' => 2,
            'return_flat_owners' => true,
        ];

        $dto = PayrollRequestDTO::fromArray($data);

        expect($dto->year)->toBe(2024);
        expect($dto->ownerId)->toBe(123);
        expect($dto->path)->toBe('1.2.3');
        expect($dto->filterParams)->toBe($data['filter_params']);
        expect($dto->rows)->toBe(50);
        expect($dto->page)->toBe(2);
        expect($dto->returnFlatOwners)->toBe(true);
    });

    it('can be created from array with minimal parameters', function () {
        $data = [
            'year' => 2024,
        ];

        $dto = PayrollRequestDTO::fromArray($data);

        expect($dto->year)->toBe(2024);
        expect($dto->ownerId)->toBeNull();
        expect($dto->path)->toBeNull();
        expect($dto->filterParams)->toBe([]);
        expect($dto->rows)->toBeNull();
        expect($dto->page)->toBeNull();
        expect($dto->returnFlatOwners)->toBe(false);
    });

    it('can convert to array', function () {
        $dto = new PayrollRequestDTO(
            year: 2024,
            ownerId: 123,
            path: '1.2.3',
            filterParams: ['test' => 'value'],
            rows: 50,
            page: 2,
            returnFlatOwners: true
        );

        $array = $dto->toArray();

        expect($array)->toBe([
            'year' => 2024,
            'owner_id' => 123,
            'path' => '1.2.3',
            'filter_params' => ['test' => 'value'],
            'rows' => 50,
            'page' => 2,
            'return_flat_owners' => true,
        ]);
    });

    it('handles null values correctly in toArray', function () {
        $dto = new PayrollRequestDTO(
            year: 2024,
            ownerId: null,
            path: null,
            filterParams: [],
            rows: null,
            page: null,
            returnFlatOwners: false
        );

        $array = $dto->toArray();

        expect($array)->toBe([
            'year' => 2024,
            'owner_id' => null,
            'path' => null,
            'filter_params' => [],
            'rows' => null,
            'page' => null,
            'return_flat_owners' => false,
        ]);
    });
});
