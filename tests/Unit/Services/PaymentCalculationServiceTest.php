<?php

declare(strict_types=1);

use App\Services\PaymentCalculationService;
use App\Utils\MathUtil;

describe('PaymentCalculationService', function () {
    beforeEach(function () {
        $this->service = new PaymentCalculationService();
    });

    describe('aggPlots', function () {
        it('can aggregate plots by owner', function () {
            $plots = [
                [
                    'owner_id' => 1,
                    'path' => '1',
                    'plot_id' => 101,
                    'pc_rel_id' => 1001,
                    'all_owner_area' => '10.500',
                    'all_owner_contract_area' => '10.500',
                    'all_owner_no_rounded' => '10.500',
                    'all_owner_no_rounded_contract' => '10.500',
                    'pu_area' => '1.000',
                    'calculation_area' => '9.500',
                    'cultivated_area' => '10.500',
                    'renta' => '100.00',
                    'charged_renta' => '50.00',
                    'unpaid_renta' => '150.00',
                    'renta_nat_info' => [],
                    'unpaid_renta_nat_arr' => [],
                    'unpaid_renta_nat_money_arr' => [],
                    'kad_ident' => 'KAD001',
                    'rep_names' => '<PERSON>e',
                    'rep_iban' => 'BG123456789',
                ],
                [
                    'owner_id' => 1,
                    'path' => '1',
                    'plot_id' => 102,
                    'pc_rel_id' => 1002,
                    'all_owner_area' => '5.250',
                    'all_owner_contract_area' => '5.250',
                    'all_owner_no_rounded' => '5.250',
                    'all_owner_no_rounded_contract' => '5.250',
                    'pu_area' => '0.500',
                    'calculation_area' => '4.750',
                    'cultivated_area' => '5.250',
                    'renta' => '50.00',
                    'charged_renta' => '25.00',
                    'unpaid_renta' => '75.00',
                    'renta_nat_info' => [],
                    'unpaid_renta_nat_arr' => [],
                    'unpaid_renta_nat_money_arr' => [],
                    'kad_ident' => 'KAD002',
                    'rep_names' => 'Jane Smith',
                    'rep_iban' => 'BG987654321',
                ],
            ];

            $result = $this->service->aggPlots($plots, 'owner');

            expect($result)->toHaveCount(1);
            
            $aggregated = array_values($result)[0];
            expect($aggregated['owner_id'])->toBe(1);
            expect($aggregated['all_owner_area'])->toBe('15.750');
            expect($aggregated['pu_area'])->toBe('1.500');
            expect($aggregated['owner_area'])->toBe('14.250');
            expect($aggregated['cultivated_area'])->toBe('15.750');
            expect($aggregated['renta'])->toBe('150.00');
            expect($aggregated['charged_renta'])->toBe('75.00');
            expect($aggregated['unpaid_renta'])->toBe('225.00');
            expect($aggregated['kad_idents_array'])->toBe(['KAD001', 'KAD002']);
            expect($aggregated['rep_names_array'])->toBe(['John Doe', 'Jane Smith']);
            expect($aggregated['rep_ibans_array'])->toBe(['BG123456789', 'BG987654321']);
        });

        it('can aggregate plots by contract', function () {
            $plots = [
                [
                    'owner_id' => 1,
                    'contract_id' => 100,
                    'plot_id' => 101,
                    'pc_rel_id' => 1001,
                    'all_owner_area' => '10.500',
                    'all_owner_contract_area' => '10.500',
                    'all_owner_no_rounded' => '10.500',
                    'all_owner_no_rounded_contract' => '10.500',
                    'pu_area' => '1.000',
                    'calculation_area' => '9.500',
                    'cultivated_area' => '10.500',
                    'renta' => '100.00',
                    'charged_renta' => '50.00',
                    'unpaid_renta' => '150.00',
                    'renta_nat_info' => [],
                    'unpaid_renta_nat_arr' => [],
                    'unpaid_renta_nat_money_arr' => [],
                    'kad_ident' => 'KAD001',
                ],
                [
                    'owner_id' => 2,
                    'contract_id' => 100,
                    'plot_id' => 102,
                    'pc_rel_id' => 1002,
                    'all_owner_area' => '5.250',
                    'all_owner_contract_area' => '5.250',
                    'all_owner_no_rounded' => '5.250',
                    'all_owner_no_rounded_contract' => '5.250',
                    'pu_area' => '0.500',
                    'calculation_area' => '4.750',
                    'cultivated_area' => '5.250',
                    'renta' => '50.00',
                    'charged_renta' => '25.00',
                    'unpaid_renta' => '75.00',
                    'renta_nat_info' => [],
                    'unpaid_renta_nat_arr' => [],
                    'unpaid_renta_nat_money_arr' => [],
                    'kad_ident' => 'KAD002',
                ],
            ];

            $result = $this->service->aggPlots($plots, 'contract');

            expect($result)->toHaveCount(1);
            
            $aggregated = array_values($result)[0];
            expect($aggregated['contract_id'])->toBe(100);
            expect($aggregated['all_owner_area'])->toBe('15.750');
            expect($aggregated['cultivated_area'])->toBe('15.750');
            expect($aggregated['renta'])->toBe('150.00');
        });
    });

    describe('sumPayments', function () {
        it('can sum two payment records', function () {
            $payment1 = [
                'plot_id' => [101],
                'pc_rel_id' => [1001],
                'all_owner_area' => '10.500',
                'all_owner_contract_area' => '10.500',
                'all_owner_no_rounded' => '10.500',
                'all_owner_no_rounded_contract' => '10.500',
                'pu_area' => '1.000',
                'owner_area' => '9.500',
                'cultivated_area' => '10.500',
                'renta' => '100.00',
                'charged_renta' => '50.00',
                'unpaid_renta' => '150.00',
                'renta_nat' => ['1' => '5.000'],
                'charged_renta_nat' => ['1' => '2.500'],
                'unpaid_renta_nat_arr' => ['1' => '7.500'],
                'unpaid_renta_nat_money_arr' => ['1' => '75.00'],
                'charged_renta_nat_values' => [],
                'plots_percent' => [],
            ];

            $payment2 = [
                'plot_id' => 102,
                'pc_rel_id' => 1002,
                'all_owner_area' => '5.250',
                'all_owner_contract_area' => '5.250',
                'all_owner_no_rounded' => '5.250',
                'all_owner_no_rounded_contract' => '5.250',
                'pu_area' => '0.500',
                'calculation_area' => '4.750',
                'cultivated_area' => '5.250',
                'renta' => '50.00',
                'charged_renta' => '25.00',
                'unpaid_renta' => '75.00',
                'renta_nat_info' => [
                    ['renta_nat_id' => '1', 'contract_renta_nat' => '2.500', 'charged_renta_nat' => '1.250']
                ],
                'unpaid_renta_nat_arr' => ['1' => '3.750'],
                'unpaid_renta_nat_money_arr' => ['1' => '37.50'],
                'plots_percent' => '50.00',
            ];

            $result = $this->service->sumPayments($payment1, $payment2);

            expect($result['plot_id'])->toBe([101, 102]);
            expect($result['pc_rel_id'])->toBe([1001, 1002]);
            expect($result['all_owner_area'])->toBe('15.750');
            expect($result['all_owner_contract_area'])->toBe('15.750');
            expect($result['pu_area'])->toBe('1.500');
            expect($result['owner_area'])->toBe('14.250');
            expect($result['cultivated_area'])->toBe('15.750');
            expect($result['renta'])->toBe('150.00');
            expect($result['charged_renta'])->toBe('75.00');
            expect($result['unpaid_renta'])->toBe('225.00');
            expect($result['renta_nat']['1'])->toBe('7.500');
            expect($result['charged_renta_nat']['1'])->toBe('3.750');
            expect($result['unpaid_renta_nat_arr']['1'])->toBe('11.250');
            expect($result['unpaid_renta_nat_money_arr']['1'])->toBe('112.50');
            expect($result['charged_renta_nat_values'][102])->toBe('1.250');
            expect($result['plots_percent'][102])->toBe('50.00');
        });

        it('handles duplicated plots correctly', function () {
            $payment1 = [
                'plot_id' => [101],
                'pc_rel_id' => [1001],
                'all_owner_area' => '10.500',
                'all_owner_contract_area' => '10.500',
                'all_owner_no_rounded' => '10.500',
                'all_owner_no_rounded_contract' => '10.500',
                'pu_area' => '1.000',
                'owner_area' => '9.500',
                'cultivated_area' => '10.500',
                'renta' => '100.00',
                'charged_renta' => '50.00',
                'unpaid_renta' => '150.00',
                'renta_nat' => [],
                'charged_renta_nat' => [],
                'unpaid_renta_nat_arr' => [],
                'unpaid_renta_nat_money_arr' => [],
                'charged_renta_nat_values' => [],
                'plots_percent' => [],
            ];

            $payment2 = [
                'plot_id' => 101, // Same plot
                'pc_rel_id' => 1001,
                'all_owner_area' => '5.250',
                'all_owner_contract_area' => '5.250',
                'all_owner_no_rounded' => '5.250',
                'all_owner_no_rounded_contract' => '5.250',
                'pu_area' => '0.500',
                'calculation_area' => '4.750',
                'cultivated_area' => '5.250',
                'renta' => '50.00',
                'charged_renta' => '25.00',
                'unpaid_renta' => '75.00',
                'renta_nat_info' => [],
                'unpaid_renta_nat_arr' => [],
                'unpaid_renta_nat_money_arr' => [],
                'plots_percent' => '50.00',
            ];

            $result = $this->service->sumPayments($payment1, $payment2, true);

            // Cultivated area should not be summed for duplicated plots
            expect($result['cultivated_area'])->toBe('10.500');
            
            // Other values should still be summed
            expect($result['all_owner_area'])->toBe('15.750');
            expect($result['renta'])->toBe('150.00');
        });
    });

    describe('fixRounding', function () {
        it('can fix small positive rounding errors', function () {
            $payments = [
                'payment1' => [
                    'charged_renta' => '100.05',
                    'renta' => '0.00',
                    'paid_renta' => '100.00',
                    'unpaid_renta' => '0.05',
                ],
            ];

            $result = $this->service->fixRounding($payments);

            expect($result['payment1']['charged_renta'])->toBe('100.00');
            expect($result['payment1']['unpaid_renta'])->toBe('0.00');
        });

        it('can fix small negative rounding errors', function () {
            $payments = [
                'payment1' => [
                    'charged_renta' => '99.95',
                    'renta' => '0.00',
                    'paid_renta' => '100.00',
                    'unpaid_renta' => '-0.05',
                ],
            ];

            $result = $this->service->fixRounding($payments);

            expect($result['payment1']['charged_renta'])->toBe('100.00');
            expect($result['payment1']['unpaid_renta'])->toBe('0.00');
        });

        it('does not fix large rounding errors', function () {
            $payments = [
                'payment1' => [
                    'charged_renta' => '100.50',
                    'renta' => '0.00',
                    'paid_renta' => '100.00',
                    'unpaid_renta' => '0.50',
                ],
            ];

            $result = $this->service->fixRounding($payments);

            // Should remain unchanged as error is too large
            expect($result['payment1']['charged_renta'])->toBe('100.50');
            expect($result['payment1']['unpaid_renta'])->toBe('0.50');
        });
    });
});
