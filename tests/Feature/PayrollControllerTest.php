<?php

declare(strict_types=1);

use App\Services\PayrollService;
use App\DTOs\PayrollResponseDTO;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

describe('PayrollController', function () {
    beforeEach(function () {
        // Mock the PayrollService
        $this->payrollService = Mockery::mock(PayrollService::class);
        $this->app->instance(PayrollService::class, $this->payrollService);
    });

    describe('GET /api/payroll/owner', function () {
        it('returns owner payroll data successfully', function () {
            $mockResponse = new PayrollResponseDTO(
                rows: [
                    [
                        'owner_id' => 1,
                        'owner_names' => 'John <PERSON>',
                        'owner_area' => '10.500',
                        'renta' => '100.00',
                        'paid_renta' => '50.00',
                        'unpaid_renta' => '50.00',
                    ]
                ],
                total: 1,
                footer: []
            );

            $this->payrollService
                ->shouldReceive('getOwnerPayroll')
                ->once()
                ->andReturn($mockResponse);

            $response = $this->getJson('/api/payroll/owner?year=2024&owner_id=1');

            $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Owner payroll data retrieved successfully',
                    'data' => [
                        'rows' => [
                            [
                                'owner_id' => 1,
                                'owner_names' => 'John Doe',
                                'owner_area' => '10.500',
                                'renta' => '100.00',
                                'paid_renta' => '50.00',
                                'unpaid_renta' => '50.00',
                            ]
                        ],
                        'total' => 1,
                        'footer' => []
                    ]
                ]);
        });

        it('validates required year parameter', function () {
            $response = $this->getJson('/api/payroll/owner');

            $response->assertStatus(422)
                ->assertJson([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => [
                        'year' => ['The year field is required.']
                    ]
                ]);
        });

        it('validates year parameter range', function () {
            $response = $this->getJson('/api/payroll/owner?year=1999');

            $response->assertStatus(422)
                ->assertJson([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => [
                        'year' => ['The year field must be at least 2000.']
                    ]
                ]);
        });

        it('handles service exceptions gracefully', function () {
            $this->payrollService
                ->shouldReceive('getOwnerPayroll')
                ->once()
                ->andThrow(new Exception('Database connection failed'));

            $response = $this->getJson('/api/payroll/owner?year=2024');

            $response->assertStatus(500)
                ->assertJson([
                    'success' => false,
                    'message' => 'Failed to retrieve owner payroll data',
                    'error' => 'Database connection failed'
                ]);
        });
    });

    describe('GET /api/payroll/owners', function () {
        it('returns owners payroll data successfully', function () {
            $mockResponse = new PayrollResponseDTO(
                rows: [
                    [
                        'owner_id' => 1,
                        'owner_names' => 'John Doe',
                        'owner_area' => '10.500',
                        'renta' => '100.00',
                    ],
                    [
                        'owner_id' => 2,
                        'owner_names' => 'Jane Smith',
                        'owner_area' => '5.250',
                        'renta' => '50.00',
                    ]
                ],
                total: 2,
                footer: [
                    [
                        'total_area' => '15.750',
                        'total_renta' => '150.00',
                    ]
                ]
            );

            $this->payrollService
                ->shouldReceive('getOwnersPayroll')
                ->once()
                ->andReturn($mockResponse);

            $response = $this->getJson('/api/payroll/owners?year=2024');

            $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Owners payroll data retrieved successfully',
                    'data' => [
                        'rows' => [
                            [
                                'owner_id' => 1,
                                'owner_names' => 'John Doe',
                            ],
                            [
                                'owner_id' => 2,
                                'owner_names' => 'Jane Smith',
                            ]
                        ],
                        'total' => 2,
                        'footer' => [
                            [
                                'total_area' => '15.750',
                                'total_renta' => '150.00',
                            ]
                        ]
                    ]
                ]);
        });

        it('handles pagination parameters', function () {
            $mockResponse = new PayrollResponseDTO(
                rows: [],
                total: 0,
                footer: []
            );

            $this->payrollService
                ->shouldReceive('getOwnersPayroll')
                ->once()
                ->andReturn($mockResponse);

            $response = $this->getJson('/api/payroll/owners?year=2024&page=2&rows=50');

            $response->assertStatus(200);
        });

        it('validates pagination parameters', function () {
            $response = $this->getJson('/api/payroll/owners?year=2024&rows=1001');

            $response->assertStatus(422)
                ->assertJson([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => [
                        'rows' => ['The rows field must not be greater than 1000.']
                    ]
                ]);
        });
    });

    describe('GET /api/payroll/owner/{ownerId}/payments', function () {
        it('returns owner payments data successfully', function () {
            $response = $this->getJson('/api/payroll/owner/123/payments?year=2024');

            $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Owner payments data retrieved successfully',
                    'data' => [
                        'rows' => [],
                        'total' => 0,
                        'footer' => []
                    ]
                ]);
        });

        it('validates required year parameter', function () {
            $response = $this->getJson('/api/payroll/owner/123/payments');

            $response->assertStatus(422)
                ->assertJson([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => [
                        'year' => ['The year field is required.']
                    ]
                ]);
        });
    });

    describe('GET /api/payroll/contract/{contractId}/payments', function () {
        it('returns contract payments data successfully', function () {
            $response = $this->getJson('/api/payroll/contract/456/payments?year=2024');

            $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Contract payments data retrieved successfully',
                    'data' => [
                        'rows' => [],
                        'total' => 0,
                        'footer' => []
                    ]
                ]);
        });

        it('handles optional annex_id parameter', function () {
            $response = $this->getJson('/api/payroll/contract/456/payments?year=2024&annex_id=789');

            $response->assertStatus(200);
        });

        it('validates annex_id parameter type', function () {
            $response = $this->getJson('/api/payroll/contract/456/payments?year=2024&annex_id=invalid');

            $response->assertStatus(422)
                ->assertJson([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => [
                        'annex_id' => ['The annex id field must be an integer.']
                    ]
                ]);
        });
    });

    afterEach(function () {
        Mockery::close();
    });
});
